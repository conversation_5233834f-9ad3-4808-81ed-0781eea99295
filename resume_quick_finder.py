#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Resume Quick Finder - A PyQt5 application for resume parsing and searching
using  API and SQLite database with a dark grey theme.
"""

import sys
import os
import sqlite3
import datetime
import json
import io
import subprocess
import google.generativeai as genai
import math
import hashlib
import shlex
import re
import html
import time
import concurrent.futures # Added for ThreadPoolExecutor
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QFileDialog, QProgressBar,
                            QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QStackedWidget,
                            QTabWidget, QTextEdit, QComboBox, QGroupBox, QSplitter, QSpinBox,
                            QCheckBox, QRadioButton, QScrollArea, QFrame, QD<PERSON>og, QFormLayout)
from PyQt5.QtGui import QIcon, QFont, QColor, QPalette
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, QSettings, QTimer
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineSettings, QWebEnginePage
from PyQt5.QtCore import QUrl
import fitz  # PyMuPDF for PDF extraction
import docx  # For DOCX extraction
import mammoth # For DOCX to HTML
import tempfile
from docx2pdf import convert as convert_docx_to_pdf # For DOCX to PDF conversion

# Configuration for path transformation
# Dynamically determine the user's home directory.
# This will be C:\Users\<USER>\Users\<username>\ that is common for the OneDrive share
COMMON_ONEDRIVE_RELATIVE_PATH = os.path.normpath(r"OneDrive - Quality People Recruitment Services/David Silverman's files - - QP - Database/JobAdder DB/QP - CV - DB")

# Specific fallback base path as requested
DAVID_FALLBACK_ONEDRIVE_BASE = os.path.normpath(r"C:\Users\<USER>\OneDrive - Quality People Recruitment Services\- QP - Database\JobAdder DB\QP - CV - DB")


def _try_robust_json_loads(json_text_blob: str):
    """
    Attempts to parse a JSON string, with pre-processing to handle common AI-generated issues.
    Tries to find the main JSON object or array within the blob.
    Returns a tuple: (parsed_data, error_message_or_none)
    """
    if not json_text_blob:
        return None, "Input text blob is empty."

    original_blob_for_error = json_text_blob[:500]  # For error messages

    processed_blob = json_text_blob.strip()  # Remove leading/trailing whitespace first

    fence_match = re.match(r"^\s*```(?:json(?:[,\s])?)?\s*\n?(.*?)\n?\s*```\s*$", processed_blob, re.DOTALL | re.IGNORECASE)
    if fence_match:
        # If the regex matches the whole processed_blob, it means it was fully wrapped.
        # Use the content inside the fences.
        processed_blob = fence_match.group(1).strip()
    else:
        # If not fully wrapped by ```...```, check for a leading "json" or "```json" line
        # that might precede the actual JSON object/array.
        # This handles cases like:
        # json
        # { ... }
        # OR
        # ```json
        # { ... } (without a closing ```)
        prefix_match = re.match(r"^\s*(?:```(?:json(?:[,\s])?)?|json)\s*\n?(?=[{\[])", processed_blob, re.IGNORECASE)
        if prefix_match:
            processed_blob = processed_blob[len(prefix_match.group(0)):].strip()

    # In this 'else' case, the original find {/[ and }/] logic will try to locate JSON.

    # If after potential stripping, the blob is empty, it's an issue.
    if not processed_blob:
        return None, f"Blob became empty after stripping markdown fences or was initially empty/whitespace. Original blob (first 500 chars): {original_blob_for_error}"

    json_to_search = processed_blob  # Use this for finding JSON markers

    # 1. Try to find the main JSON structure (object or array)
    json_str = None
    first_brace = json_to_search.find('{')
    first_bracket = json_to_search.find('[')

    if first_brace == -1 and first_bracket == -1: # No JSON markers found
        return None, f"Could not find JSON object or array start markers. Content after fence stripping (first 500 chars): {json_to_search[:500]}. Original blob (first 500 chars): {original_blob_for_error}"

    if first_brace != -1 and (first_bracket == -1 or first_brace < first_bracket):
        last_brace = json_to_search.rfind('}')
        if last_brace > first_brace:
            json_str = json_to_search[first_brace : last_brace + 1]
    elif first_bracket != -1:
        last_bracket = json_to_search.rfind(']')
        if last_bracket > first_bracket:
            json_str = json_to_search[first_bracket : last_bracket + 1]

    if not json_str:
        return None, f"Could not extract a valid JSON substring after fence stripping. Content after fence stripping (first 500 chars): {json_to_search[:500]}. Original blob (first 500 chars): {original_blob_for_error}"

    # 2. Pre-processing cleanups
    json_str = re.sub(r'\bNone\b', 'null', json_str)
    json_str = re.sub(r'\bTrue\b', 'true', json_str)
    json_str = re.sub(r'\bFalse\b', 'false', json_str)
    json_str = re.sub(r',\s*([\}\]])', r'\1', json_str) # Remove trailing commas
    # Attempt to fix missing commas between a list/object and the next key
    # e.g., ] "key" -> ], "key"  OR  } "key" -> }, "key"
    json_str = re.sub(r'([\}\]])\s*(")', r'\1,\2', json_str)

    try:
        return json.loads(json_str), None
    except json.JSONDecodeError as e:
        error_msg = (
            f"JSONDecodeError: {e.msg} (line {e.lineno} col {e.colno} pos {e.pos}).\n"
            f"Cleaned JSON segment (first 500 chars):\n-----\n{json_str[:500]}\n-----\n"
            f"Original blob (first 500 chars):\n-----\n{original_blob_for_error}\n-----"
        )
        return None, error_msg

def transform_path_for_storage(original_path: str) -> str:
    """
    Transforms an absolute path to a "relative" path by stripping a predefined prefix.
    """
    if not original_path:
        return ""
    norm_original_path = os.path.normpath(original_path)

    if PATH_PREFIX_TO_STRIP_AND_PREPEND and \
       norm_original_path.lower().startswith(PATH_PREFIX_TO_STRIP_AND_PREPEND.lower()):

        prefix_len = len(PATH_PREFIX_TO_STRIP_AND_PREPEND)
        transformed = norm_original_path[prefix_len:]
        # Ensure the transformed path starts with a separator if it's not empty
        if transformed and not transformed.startswith(os.sep):
            transformed = os.sep + transformed
        elif not transformed: # If original_path was exactly the prefix
            return os.sep
        return transformed
    return norm_original_path

# Constants
APP_TITLE = "Resume Quick Finder"
DB_PATH = "resume_database.db"
DB_PATH_SETTING_KEY = "databasePath" # QSettings key for custom DB path
DOC_BASE_PATH_SETTING_KEY = "resumeDocumentBasePath" # QSettings key for custom document base path
SUPPORTED_FORMATS = ["pdf", "doc", "docx"]

# Dark theme stylesheet
DARK_THEME = """
QWidget {
    background-color: #2D2D30;
    color: #FFFFFF;
    font-size: 10pt;
}

QMainWindow {
    background-color: #2D2D30;
}

QTabWidget::pane {
    border: 1px solid #3E3E42;
    background-color: #2D2D30;
}

QTabBar::tab {
    background-color: #3E3E42;
    color: #FFFFFF;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #007ACC;
}

QPushButton {
    background-color: #007ACC;
    color: #FFFFFF;
    border: none;
    padding: 10px 15px; /* Increased vertical padding, slightly reduced horizontal */
    border-radius: 4px;
}

QPushButton:hover {
    background-color: #1C97EA;
}

QPushButton:pressed {
    background-color: #0062A3;
}

QPushButton#deleteResumesButton {
    background-color: #D32F2F; /* A shade of red */
    color: #FFFFFF;
    border: 1px solid #B71C1C; /* Darker red border */
    padding: 10px 15px;
    border-radius: 4px;
}

QPushButton#deleteResumesButton:hover {
    background-color: #E53935; /* Lighter red on hover */
}

QPushButton#deleteResumesButton:pressed {
    background-color: #C62828; /* Darker red when pressed */
}
/* Specific styles for buttons in ResumeDetailWidget */
QPushButton#detailPreviewButton, QPushButton#detailCloseButton {
    padding: 4px 10px; /* Reduced padding for a more compact size */
    border-radius: 8px; /* More rounded edges */
}

QPushButton:disabled {
    background-color: #3E3E42;
    color: #9D9D9D;
}

QLineEdit, QTextEdit, QComboBox {
    background-color: #3E3E42;
    color: #FFFFFF;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
}

QTableWidget {
    background-color: #2D2D30;
    alternate-background-color: #3E3E42;
    gridline-color: #3E3E42;
    border: 1px solid #3E3E42;
}

QTableWidget::item {
    padding: 4px;
}

QTableWidget::item:selected {
    background-color: #007ACC;
}

QTableWidget::item:focus {
    /* For items that are focused but not necessarily selected (e.g., via keyboard) */
    outline: none;
}

QTableWidget::item:selected:focus {
    /* For items that are both selected and focused (typical for mouse click) */
    background-color: #007ACC; /* Ensure background matches selected state */
    border: none;              /* Explicitly remove any border */
    outline: none;             /* Explicitly remove any outline */
}

QHeaderView::section {
    background-color: #3E3E42;
    color: #FFFFFF;
    padding: 4px;
    border: 1px solid #555555;
}

QTableWidget QTableCornerButton::section {
    background-color: #3E3E42; /* Match header background */
    border: 1px solid #555555;   /* Match header border */
    /* You could add other properties here if needed, like padding */
}

QProgressBar {
    border: 1px solid #3E3E42;
    border-radius: 4px;
    text-align: center;
    background-color: #3E3E42;
}

QProgressBar::chunk {
    background-color: #007ACC;
    width: 10px;
}

QScrollBar:vertical {
    border: none;
    background-color: #2D2D30; /* Match main GUI background */
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    min-height: 25px; /* Slightly increased min-height for better grab area */
    border-radius: 6px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
    background: none;
}

QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

QScrollBar:horizontal {
    border: none;
    background-color: #2D2D30; /* Match main GUI background */
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    min-width: 25px; /* Slightly increased min-width for better grab area */
    border-radius: 6px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
    background: none;
}

QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
    background: none;
}

QGroupBox {
    border: 1px solid #3E3E42;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 16px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
}

QSplitter::handle {
    background-color: #3E3E42;
}

QFrame#line {
    background-color: #3E3E42;
}
"""

class NumericTableWidgetItem(QTableWidgetItem):
    def __init__(self, numeric_value, display_text=None):
        # If display_text is not provided, use the string representation of numeric_value
        if display_text is None:
            display_text = str(numeric_value)
        super().__init__(display_text)
        # Store the actual numeric value for comparison
        try:
            self.numeric_value = float(numeric_value)
        except ValueError:
            # Fallback if numeric_value cannot be converted to float (e.g., empty string for unranked items)
            # Assign a value that would typically sort them to one end or handle as string.
            # For empty strings in Rank/Score when not ranked, they won't be NumericTableWidgetItems anyway.
            self.numeric_value = -float('inf') # Or handle appropriately if empty strings are passed

    def __lt__(self, other):
        # Override the less-than operator for sorting
        if isinstance(other, NumericTableWidgetItem):
            return self.numeric_value < other.numeric_value
        # Fallback to default string comparison if other is not a NumericTableWidgetItem
        return super().__lt__(other)

class SearchWorker(QThread):
    """Thread for performing database search operations in the background"""
    progress_signal = pyqtSignal(int)  # Progress percentage (0-100)
    status_signal = pyqtSignal(str)    # Status message updates
    results_signal = pyqtSignal(list, list)  # (results, highlight_terms)
    error_signal = pyqtSignal(str)     # Error messages
    finished_signal = pyqtSignal()     # Search completed

    def __init__(self, db_manager, search_type, **kwargs):
        super().__init__()
        self.db_manager = db_manager
        self.search_type = search_type  # 'boolean', 'keywords', 'all'
        self.kwargs = kwargs
        self._cancelled = False

    def cancel(self):
        """Cancel the current search operation"""
        self._cancelled = True

    def run(self):
        """Execute the search operation in background thread"""
        try:
            self.status_signal.emit("Starting search...")
            self.progress_signal.emit(10)

            if self._cancelled:
                return

            if self.search_type == 'boolean':
                self._perform_boolean_search()
            elif self.search_type == 'keywords':
                self._perform_keyword_search()
            elif self.search_type == 'all':
                self._load_all_resumes()
            else:
                self.error_signal.emit(f"Unknown search type: {self.search_type}")
                return

        except Exception as e:
            self.error_signal.emit(f"Search error: {str(e)}")
        finally:
            self.finished_signal.emit()

    def _perform_boolean_search(self):
        """Perform boolean search operation"""
        query_text = self.kwargs.get('query_text', '')
        search_raw_text_only = self.kwargs.get('search_raw_text_only', False)

        self.status_signal.emit(f"Searching for: {query_text}")
        self.progress_signal.emit(30)

        if self._cancelled:
            return

        # Perform the actual database search
        results, highlight_terms = self.db_manager.search_resumes_boolean(
            query_text, search_raw_text_exclusively=search_raw_text_only
        )

        if self._cancelled:
            return

        self.progress_signal.emit(80)
        self.status_signal.emit(f"Found {len(results)} results")
        self.progress_signal.emit(100)

        self.results_signal.emit(results, highlight_terms)

    def _perform_keyword_search(self):
        """Perform keyword-based search operation"""
        search_keywords = self.kwargs.get('search_keywords', [])

        self.status_signal.emit(f"Searching with keywords: {', '.join(search_keywords)}")
        self.progress_signal.emit(30)

        if self._cancelled:
            return

        # Perform the actual database search
        results = self.db_manager.search_resumes(search_keywords)

        if self._cancelled:
            return

        self.progress_signal.emit(80)
        self.status_signal.emit(f"Found {len(results)} results")
        self.progress_signal.emit(100)

        # For keyword search, highlight terms are the keywords themselves
        self.results_signal.emit(results, search_keywords)

    def _load_all_resumes(self):
        """Load all resumes from database"""
        self.status_signal.emit("Loading all resumes...")
        self.progress_signal.emit(30)

        if self._cancelled:
            return

        # Get total count first for better progress reporting
        try:
            # Quick count query for progress estimation
            conn = self.db_manager._get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM resumes")
            total_count = cursor.fetchone()[0]
            conn.close()

            self.status_signal.emit(f"Loading {total_count} resumes...")
            self.progress_signal.emit(50)

            if self._cancelled:
                return

        except Exception as e:
            # If count fails, continue without progress estimation
            total_count = 0

        # Perform the actual database query
        results = self.db_manager.get_all_resumes()

        if self._cancelled:
            return

        self.progress_signal.emit(90)
        self.status_signal.emit(f"Loaded {len(results)} resumes")
        self.progress_signal.emit(100)

        # No highlight terms for load all
        self.results_signal.emit(results, [])


class AIQueryAnalyzer(QThread):
    """Thread for analyzing search queries using  API"""
    complete_signal = pyqtSignal(dict) # Will emit {'boolean_query': str, 'explanation': str}
    error_signal = pyqtSignal(str)

    def __init__(self, search_query, api_key, db_tags=None): # Added db_tags
        super().__init__()
        self.search_query = search_query
        self.api_key = api_key
        self.db_tags = db_tags if db_tags else []
        # Limit the number of tags to avoid overly long prompts, prioritize more common/important ones if possible
        self.db_tags = self.db_tags[:150] # Example limit: first 150 unique tags

    def run(self):
        try:
            # Configure Gemini API
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel('gemini-1.5-flash-latest') # Using a potentially more capable model

            db_tags_info = ""
            if self.db_tags:
                db_tags_info = f"""
            The following are existing tags from the resume database.
            If the user's query *clearly and directly implies* one or more of these tags,
            you may incorporate them into the boolean_query. However, prioritize terms
            explicitly found in or directly inferred from the user's query.
            Do not include database tags that are only loosely related or not strongly supported by the user's input.
            Relevant Existing Tags: {', '.join(self.db_tags)}
            """
            # Create a prompt for the AI to analyze the search query
            prompt = f"""\
            Analyze the following natural language job requirements or search query:
            {self.search_query}
            {db_tags_info}

            Your primary task is to understand the user's description of a desired candidate profile or job role,
            and then convert this understanding into a structured **boolean search query string**.
            This boolean query string will be used to search a resume database.
            The goal is to create a **simple and effective** boolean query for initial database filtering.

            - Identify and extract key job titles, roles, or seniority levels (e.g., 'Senior Software Engineer', 'Project Manager', 'Lead Developer').
              **If the user specifies a job title like 'Program Scheduler', prioritize using that exact title in the query (e.g., "Program Scheduler") rather than a broader synonym like 'Program Manager', unless the user's text also strongly implies the synonym is acceptable or interchangeable for the search.**
            - Extract core skills, technologies, or qualifications explicitly mentioned (e.g., 'Java', 'AWS', 'Agile methodologies', 'financial modeling').
            - Consider responsibilities or experience areas if they are central to the query (e.g., 'experience managing large teams', 'background in data analysis').

            - Construct a **concise** `boolean_query` focusing on the **primary job title/role** and **2-3 most critical and distinguishing skills, technologies, or contextual keywords** (e.g., 'Defence' for industry context).
            - Use AND, OR, and parentheses () for grouping.
            - Use double quotes for exact phrases (e.g., "Project Manager", "SQL Server").
            - **Avoid making the boolean query overly long or specific by including numerous secondary details, responsibilities, or very granular phrases.** The aim is not to perfectly mirror every word of the input, but to capture the *essence* for a good initial search.
            - Regarding 'Relevant Existing Tags': Only use these if they *perfectly represent* an extracted role, core skill, or key qualification
              from the user's query. Prioritize the user's own wording and the direct interpretation of their needs over simply matching to existing tags.

            Also, provide a brief explanation of your reasoning. The explanation can elaborate on other nuances from the user's query that might be too detailed for the boolean string itself.

            Return your analysis as a JSON object with the following structure:
            {{
                "boolean_query": "your generated boolean query string here",
                "explanation": "your brief explanation here"
            }}
            Example for a complex query: If the user query is "Senior Java developer with Spring Boot and AWS experience, preferably with a background in finance and strong communication skills",
            your JSON output might be:
            {{
                "boolean_query": "\\"Senior Java Developer\\" AND \\"Spring Boot\\" AND \\"AWS\\"",
                "explanation": "The core requirements are a Senior Java Developer with Spring Boot and AWS. A finance background and communication skills are noted as preferences but omitted from the boolean query for a broader initial search, they can be assessed later."
            }}
            If the user query is very simple, like "TSPV clearance", the boolean_query might just be "\\"TSPV clearance\\"".
            Ensure the boolean_query is a single, well-formed string.
            """

            response = model.generate_content(prompt)
            response_text = response.text

            analysis_result, error = _try_robust_json_loads(response_text)
            if analysis_result is not None:
                if "boolean_query" not in analysis_result or "explanation" not in analysis_result:
                    self.error_signal.emit(f"AI response missing 'boolean_query' or 'explanation' key. Response: {response_text[:500]}")
                    self.complete_signal.emit({"boolean_query": "", "explanation": "Error: AI response did not follow expected format."})
                    return
                self.complete_signal.emit(analysis_result)
            else:
                self.error_signal.emit(f"Failed to parse AI query analysis results: {error}")
                self.complete_signal.emit({"boolean_query": "", "explanation": f"Error: Could not parse AI response. {error}"})
        except Exception as e:
            self.error_signal.emit(f"Error in AI query analysis: {str(e)}")
            self.complete_signal.emit({"boolean_query": "", "explanation": f"Error: Exception during AI analysis. {str(e)}"})


class ResumeParser(QThread):
    """Thread for parsing resumes using  API"""
    progress_signal = pyqtSignal(int)
    complete_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)

    def __init__(self, file_paths, api_key, db_manager, auto_convert_docx_to_pdf=False): # Added auto_convert_docx_to_pdf
        super().__init__()
        self.file_paths = file_paths
        self.api_key = api_key
        self.db_manager = db_manager
        self.auto_convert_docx_to_pdf = auto_convert_docx_to_pdf

    def _extraction_worker(self, file_path):
        """Worker function for ThreadPoolExecutor to extract text and hash."""
        try:
            current_filename = os.path.basename(file_path)
            # extract_text_from_document now returns (text, effective_file_path)
            text, effective_file_path = self.extract_text_from_document(file_path)

            # Transform the path for storage
            effective_file_path_for_storage = transform_path_for_storage(effective_file_path)

            # Update filename if the effective_file_path changed (e.g., .docx to .pdf)
            current_filename = os.path.basename(effective_file_path)
            content_hash = self._calculate_sha256(text)
            return {'file_path': effective_file_path_for_storage, 'filename': current_filename, 'text': text, 'content_hash': content_hash, 'error': None}
        except Exception as e:
            self.error_signal.emit(f"Error in extraction worker for {os.path.basename(file_path)}: {str(e)}")
            return {'file_path': file_path, 'filename': os.path.basename(file_path), 'text': None, 'content_hash': None, 'error': str(e)}

    def run(self):
        # Optimized batch sizes based on file count
        total_files = len(self.file_paths)

        # Dynamic batch sizing for better performance
        if total_files <= 10:
            BATCH_SIZE = 2  # Small batches for few files
        elif total_files <= 50:
            BATCH_SIZE = 3  # Standard batch size
        elif total_files <= 200:
            BATCH_SIZE = 5  # Larger batches for medium datasets
        else:
            BATCH_SIZE = 8  # Maximum batch size for large datasets

        try:
            # Configure Gemini API
            genai.configure(api_key=self.api_key)
            # Use faster model for large batches
            model_name = 'gemini-2.0-flash' if total_files <= 100 else 'gemini-1.5-flash'
            model = genai.GenerativeModel(model_name)

            if total_files == 0:
                self.complete_signal.emit([])
                return

            self.processed_files_count = 0 # For progress tracking during extraction

            # --- Phase 1: Optimized Concurrent Text Extraction ---
            self.error_signal.emit("Starting optimized concurrent text extraction phase...")
            extracted_data_results = [] # List of dicts from _extraction_worker

            # Aggressive threading for large datasets
            num_cores = os.cpu_count() or 1

            # Scale workers based on dataset size and system capabilities
            if total_files <= 20:
                max_workers = min(4, num_cores)
            elif total_files <= 100:
                max_workers = min(8, num_cores * 2)
            else:
                # For large datasets, use more aggressive threading
                max_workers = min(16, num_cores * 3)

            # Ensure we don't exceed file count
            num_workers = min(max_workers, total_files)

            self.error_signal.emit(f"Using {num_workers} worker threads for text extraction (system cores: {num_cores}, files: {total_files})")

            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                future_to_path = {executor.submit(self._extraction_worker, fp): fp for fp in self.file_paths}
                for future in concurrent.futures.as_completed(future_to_path):
                    try:
                        data = future.result()
                        extracted_data_results.append(data)
                    except Exception as exc:
                        filepath_for_error = future_to_path[future]
                        self.error_signal.emit(f"Unhandled exception for {os.path.basename(filepath_for_error)} during future processing: {exc}")
                        extracted_data_results.append({'file_path': filepath_for_error, 'filename': os.path.basename(filepath_for_error), 'text': None, 'content_hash': None, 'error': str(exc)})

                    self.processed_files_count += 1
                    # Extraction phase is considered the first 50% of the total progress
                    current_progress = int((self.processed_files_count / total_files) * 50)
                    self.progress_signal.emit(current_progress)

            self.error_signal.emit("Concurrent text extraction phase complete.")

            # --- Phase 2: Duplicate Checking and Data Preparation for AI ---
            all_resume_texts_for_prompt = []
            all_raw_texts_for_db = []
            all_file_info = []

            try:
                existing_resumes_identifiers = self.db_manager.get_resume_hashes_and_filenames()
                existing_hashes = {item['content_hash'] for item in existing_resumes_identifiers if item['content_hash']}
                existing_filenames = {item['filename'] for item in existing_resumes_identifiers}
            except Exception as e:
                self.error_signal.emit(f"Error fetching existing resume identifiers: {str(e)}. Proceeding without duplicate check.")
                existing_hashes = set()
                existing_filenames = set()

            batch_processed_hashes = set() # To track hashes within the current batch

            # Process results from the concurrent extraction phase
            for result_data in extracted_data_results:
                file_path = result_data['file_path']
                current_filename = result_data['filename']
                text = result_data['text']
                content_hash = result_data['content_hash']
                error = result_data['error']

                if error or text is None:
                    # Error should have been emitted by the worker or extract_text_from_document
                    # self.error_signal.emit(f"Skipping file {current_filename} due to extraction error: {error}")
                    continue # Skip this file if extraction failed

                # Duplicate checks
                if content_hash in existing_hashes:
                    self.error_signal.emit(f"Skipping duplicate (content match - DB): {current_filename}")
                    continue
                if content_hash in batch_processed_hashes:
                    self.error_signal.emit(f"Skipping duplicate (content match - batch): {current_filename}")
                    continue
                if current_filename in existing_filenames:
                    self.error_signal.emit(f"Skipping duplicate (filename match - DB): {current_filename}. Consider renaming if it's a different resume.")
                    continue

                batch_processed_hashes.add(content_hash)

                raw_text_for_db = text[:50000]
                limited_text_for_ai = text[:45000]

                all_resume_texts_for_prompt.append(limited_text_for_ai)
                all_raw_texts_for_db.append(raw_text_for_db)
                all_file_info.append({
                    'filename': current_filename,
                    'file_path': file_path,
                    'upload_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'content_hash': content_hash
                })

            if not all_file_info: # Check if there's anything to process after initial filtering
                self.error_signal.emit("No valid resumes to process")
                self.complete_signal.emit([])
                return

            # Process resumes in smaller batches
            all_parsed_results_from_ai = []
            num_total_to_process = len(all_file_info)

            for i in range(0, num_total_to_process, BATCH_SIZE):
                # Calculate progress for AI processing phase (50% to 99% of total progress)
                # Base progress on the number of batches processed
                # Example: if 10 files, BATCH_SIZE 3, batches are (0,1,2), (3,4,5), (6,7,8), (9)
                # Progress for AI part should go from 50% to 99%
                # Let's say current_batch_start_index is 'i'
                ai_processing_progress = 50 + int((i / num_total_to_process) * 49)
                self.progress_signal.emit(ai_processing_progress)

                current_batch_file_info = all_file_info[i:i + BATCH_SIZE]
                current_batch_resume_texts = all_resume_texts_for_prompt[i:i + BATCH_SIZE]
                current_batch_raw_texts_for_db = all_raw_texts_for_db[i:i + BATCH_SIZE]

                if not current_batch_file_info: # Should not happen if loop condition is correct
                    continue

                # Prepare items for this specific batch's prompt
                batch_prompt_items = [
                    {'text': text, 'filename': fi['filename']}
                    for text, fi in zip(current_batch_resume_texts, current_batch_file_info)
                ]

                # Optimize batch processing with retry logic and adaptive delays
                batch_number = i//BATCH_SIZE + 1
                total_batches = math.ceil(num_total_to_process / BATCH_SIZE)

                # Adaptive delay and retry strategy based on dataset size
                if num_total_to_process <= 20:
                    batch_delay = 0.5
                    max_retries = 3
                elif num_total_to_process <= 100:
                    batch_delay = 1.0
                    max_retries = 2
                else:
                    batch_delay = 1.5
                    max_retries = 1

                self.error_signal.emit(f"Processing batch {batch_number}/{total_batches} (resumes {i+1} to {min(i+BATCH_SIZE, num_total_to_process)} of {num_total_to_process})")

                # Retry logic for AI processing
                batch_success = False
                for retry_attempt in range(max_retries + 1):
                    try:
                        batch_prompt = self.create_batch_prompt(batch_prompt_items)
                        response = model.generate_content(batch_prompt)

                        if response and response.text:
                            response_text = response.text
                            parsed_batch_results = self.parse_batch_response(response_text, current_batch_file_info, current_batch_raw_texts_for_db)
                            all_parsed_results_from_ai.extend(parsed_batch_results)
                            batch_success = True
                            self.error_signal.emit(f"Batch {batch_number} completed successfully.")
                            break
                        else:
                            if retry_attempt < max_retries:
                                self.error_signal.emit(f"Empty response for batch {batch_number}, retry {retry_attempt + 1}/{max_retries}")
                                time.sleep(batch_delay * (retry_attempt + 1))
                            else:
                                raise Exception("Empty response after all retries")

                    except Exception as e:
                        if retry_attempt < max_retries:
                            self.error_signal.emit(f"Error processing batch {batch_number}, retry {retry_attempt + 1}/{max_retries}: {str(e)}")
                            time.sleep(batch_delay * (retry_attempt + 1))
                        else:
                            self.error_signal.emit(f"Error processing batch {batch_number} after all retries: {str(e)}. Creating fallback data.")
                            break

                # Create fallback data if batch processing failed
                if not batch_success:
                    fallback_batch = [self._create_fallback_data_with_file_info(fi, rt) for fi, rt in zip(current_batch_file_info, current_batch_raw_texts_for_db)]
                    all_parsed_results_from_ai.extend(fallback_batch)

                # Adaptive delay between batches (except for the last batch)
                if i + BATCH_SIZE < num_total_to_process:
                    time.sleep(batch_delay)

            # Update progress to 100%
            self.progress_signal.emit(100)
            self.complete_signal.emit(all_parsed_results_from_ai)

        except Exception as e:
            self.error_signal.emit(f"Error in resume parsing thread: {str(e)}")
            self.complete_signal.emit([])

    def create_batch_prompt(self, resume_details_list):
        """Create a batch processing prompt with all resumes"""
        # Instructions for the model
        instructions = """I
        Extract the following detailed information from each resume:

        0. Meta Information (include this in your JSON output for each resume):
           - original_filename: The filename provided as 'Filename: ...' before the resume text. This is crucial for mapping.
        1. Basic Information:
           - Full Name: Extract the candidate's full name. IMPORTANT: Prioritize extracting the name from the document content. However, if the name is ambiguous or not clearly identifiable in the content, use the provided filename (given as 'Filename: ...' before each resume text) to infer the full name. The full name MUST be a person's name (e.g., "John Doe", "Maria Garcia"). It should NOT be a job title, role, or generic term (e.g., avoid "Software Engineer", "Manager", "CV", "Resume"). If a definitive person's name cannot be determined from either the content or the filename, set full_name to "Unknown".
           - Email Address: Extract the candidate's primary email address. If multiple are clearly listed for the same candidate, pick the first one or the most professional-looking one.
           - Phone Number: Extract ONLY ONE primary phone number. If multiple are listed, select the most prominent or first one.
             **CRITICAL FORMATTING:** Reformat the extracted phone number to a standardized international format, preferably E.164 (e.g., "+***********" for an Australian number, or "+***********" for a US number).
             - Remove all spaces, hyphens, parentheses, and other non-digit characters from the subscriber number part, keeping only the leading '+' if present for the country code.
             - If a country code (e.g., "+61") is explicitly present, preserve it and ensure the subscriber number is clean.
             - For numbers that appear to be local (e.g., Australian numbers starting with "04", "02", "03", "07", "08"), attempt to convert them to international format by adding the appropriate country code (e.g., "+61" for Australia, so "0412 345 678" should become "+***********").
             - If the country code cannot be reliably determined or the number format is highly unusual, return the number with as many non-digit characters removed as possible, prioritizing a clean digit string.
             - The final output for this field MUST be a single string. Example: "0430 971 412" should become "+***********" if Australian context is clear. "+61 ***********" should become "+***********".
           - Location: Extract ONLY ONE primary location (e.g., "City, State, Country" or "City, Country"). If multiple locations are mentioned (e.g., "willing to relocate to X or Y"), pick the current or most prominent one. Return a single string for the location.
           - Security Clearance: Extract ONLY ONE primary security clearance level. Look for "NV1", "NV2", "Baseline", "TSPV". If multiple are mentioned, prioritize the highest or most comprehensive one (e.g., TSPV > NV2 > NV1 > Baseline). If only one is found, use that. If none of these specific terms are found, set this field to an empty string or "Unknown". Return a single string for the clearance level. Do not include any other words.

        2. Professional Profile:
           - Key Professional Attributes: List up to 5-7 distinct professional qualities, characteristics, or strengths (e.g., "Proactive", "Detail-oriented", "Team Player").
           - Career Objectives / Summary / Target Roles: A concise summary of the candidate's career goals or professional summary statement.

        3. Skills:
           - Technical Skills: Comprehensive list of programming languages, software, tools, frameworks, operating systems, databases, hardware, etc.
           - Soft Skills: Interpersonal skills like communication, leadership, teamwork, problem-solving, etc.
           - Domain-Specific Skills: Expertise related to particular industries or fields (e.g., "Financial Modeling", "Healthcare Regulations").
           - Certifications (as skills): List certifications if mentioned within skill sections, even if also captured in the dedicated 'Certifications' field.

        4. Experience:
           - Work History: For each role, extract:
             - Company Name
             - Job Title/Position
             - Dates of Employment (Start and End, as specific as possible)
             - Key Responsibilities and Achievements (summarized bullet points or short paragraph per role)
           - Total Years of Experience: Estimate total relevant years of experience if stated or inferable.
           - Years of Experience in Specific Fields/Technologies: If mentioned (e.g., "5 years in Python development").

        5. Education:
           - Degrees: For each degree, extract:
             - Degree Name (e.g., "Bachelor of Science in Computer Science")
             - Institution Name
             - Graduation Date (or expected graduation date)
             - Relevant Coursework or Thesis (if mentioned and concise)

        6. Certifications:
           - Professional Certifications: List all professional certifications, including:
             - Certification Name
             - Issuing Body
             - Date Obtained/Expiry Date (if available)

        7. Languages:
           - Programming Languages: List programming languages and self-assessed proficiency levels (e.g., "Python (Expert)", "Java (Intermediate)").
           - Human Languages: List spoken/written human languages and proficiency levels (e.g., "English (Native)", "Spanish (Fluent)").

        8. Projects:
           - Notable Projects: For each significant project, extract:
             - Project Name/Title
             - Brief Description of the project, technologies used, and candidate's role/contributions.

        9. Resume Tags:
           - Generate a list of 5-15 concise keywords or tags that best summarize the core content of this resume.
           - Focus on specific skills, technologies, job titles/roles, industries, key qualifications, and certifications.
           - These tags should be suitable for quick filtering and searching.
           - Example: ["Java", "Spring Boot", "Microservices", "AWS", "Software Engineer", "Financial Services", "Agile"]

        Format the response for EACH resume as a JSON object with these keys:
        original_filename (MUST be the filename provided in the input 'Filename: ...' for this resume),
        full_name, email, phone, location, security_clearance, attributes, objectives,
        skills (as a dictionary with sub-keys: technical, soft, domain_specific, certifications_as_skills),
        experience (as a list of objects, each with company, title, dates, responsibilities; also include total_years_experience and specific_years_experience if found),
        education (as a list of objects, each with degree, institution, graduation_date, coursework),
        certifications (as a list of objects, each with name, issuer, date),
        languages (as a dictionary with sub-keys: programming, human),
        projects (as a list of objects, each with name, description)

        For list fields (attributes, skills, experience, education, certifications, languages, projects),
        return arrays of items.

        CRITICALLY IMPORTANT: After EACH resume's complete JSON object, you MUST include the marker "/resumesave" on its own new line to indicate
        the end of that resume's data. This marker is crucial for parsing your response correctly.

        IMPORTANT JSON FORMATTING: Ensure that each JSON object you generate is syntactically correct and strictly follows JSON formatting rules.
        Pay very close attention to:
        1. Commas: Ensure all items in a list are separated by commas, and that there is NO comma after the last item if it's the final element of an enclosing object or array. Ensure all key-value pairs in an object are separated by commas (except the last pair).
        2. Quotes: All keys and string values must be enclosed in double quotes.
        3. Brackets and Braces: Ensure proper matching and nesting of square brackets [] for arrays/lists and curly braces {} for objects.
        4. Null values: Use `null` for missing optional values within objects, not empty strings, unless the field is inherently a string that can be empty. For empty lists, use `[]`.

        Process each resume independently and provide a complete JSON object for each one.
        Ensure all extracted text is clean and free of extraneous formatting characters.
        If a specific piece of information is not found, represent it appropriately (e.g., empty string, empty list, or null for non-critical optional fields within objects).
        Pay close attention to the structure requested for complex fields like 'skills', 'experience', 'education', 'certifications', 'languages', and 'projects'.
        """
        # Update JSON structure in prompt to include resume_tags
        instructions = instructions.replace("projects (as a list of objects, each with name, description)", "projects (as a list of objects, each with name, description),\n        resume_tags (as a list of strings)")
        # Add each resume to the prompt
        prompt = instructions + "\n\nHere are the resumes to process (ensure each output JSON includes 'original_filename' and is followed by '/resumesave'):\n\n"

        for i, resume_detail in enumerate(resume_details_list):
            text = resume_detail['text']
            filename = resume_detail['filename']
            prompt += f"RESUME #{i+1} (Filename: {filename}):\n{text}\n\n"

        prompt += "\nRemember to include the '/resumesave' marker after each resume's JSON data."

        return prompt

    def parse_batch_response(self, response_text, file_info_list_for_batch, raw_texts_list_for_batch):
        """Parse the batch response and extract individual resume data"""
        filename_to_details_map = {
            fi['filename']: {'info_dict': fi, 'raw_text': rt}
            for fi, rt in zip(file_info_list_for_batch, raw_texts_list_for_batch)
        }

        # Initialize results with fallbacks for all expected files.
        # These will be overwritten if successful parsing occurs for a given file.
        parsed_results_by_input_filename = {
            fi['filename']: self._create_fallback_data_with_file_info(fi, filename_to_details_map[fi['filename']]['raw_text'])
            for fi in file_info_list_for_batch # Ensures every input file has an initial entry
        }

        # Tracks filenames successfully matched by AI's 'original_filename' field
        matched_input_files_by_ai_filename = set()

        parts = response_text.split("/resumesave")

        # Store successfully parsed AI blocks. Each item will be a dict:
        # {'data': dict, 'original_filename_from_ai': str_or_none, 'source_part_index': int}
        successfully_parsed_ai_blocks = []
        # Store AI blocks that are missing 'original_filename'
        ai_blocks_for_ordered_assignment = []

        for part_idx, part_str in enumerate(parts):
            part_trimmed = part_str.strip()
            if not part_trimmed: # Skip empty parts (e.g., after the last /resumesave)
                continue

            resume_data_from_ai, error = _try_robust_json_loads(part_trimmed)

            if resume_data_from_ai is None:
                num_files_in_batch = len(file_info_list_for_batch)
                log_message_prefix = "Info:" if part_idx >= num_files_in_batch else "Warning:"
                self.error_signal.emit(
                    f"{log_message_prefix} ResumeParser: Failed to parse JSON from AI response part {part_idx + 1} (batch size {num_files_in_batch}): {error}. This AI part will be skipped."
                )
                continue
            else: # Successfully parsed JSON from this AI part
                ai_original_filename = resume_data_from_ai.get('original_filename')
                successfully_parsed_ai_blocks.append({
                    'data': resume_data_from_ai,
                    'original_filename_from_ai': ai_original_filename,
                    'source_part_index': part_idx
                })

        # --- Primary Matching Pass (Strict original_filename) ---
        for ai_block in successfully_parsed_ai_blocks:
            ai_fn = ai_block['original_filename_from_ai']
            source_part_idx = ai_block['source_part_index']

            if ai_fn: # AI block has an 'original_filename'
                if ai_fn in filename_to_details_map: # And it's a filename from the current input batch
                    if ai_fn not in matched_input_files_by_ai_filename: # And this input file hasn't been matched yet
                        # This is a good, unique match.
                        details = filename_to_details_map[ai_fn]
                        processed_data = self.process_resume_data(ai_block['data'])
                        processed_data.update(details['info_dict'])
                        processed_data['raw_text'] = details['raw_text']

                        parsed_results_by_input_filename[ai_fn] = processed_data
                        matched_input_files_by_ai_filename.add(ai_fn)
                        self.error_signal.emit(f"ResumeParser: Successfully matched AI part #{source_part_idx + 1} to input file '{ai_fn}' using 'original_filename'.")
                    else:
                        # This input filename was already matched by a previous AI block.
                        self.error_signal.emit(f"ResumeParser Warning: AI part #{source_part_idx + 1} also claims 'original_filename': '{ai_fn}', but this input file was already matched by another AI block. Discarding this AI block to prevent cross-talk.")
                else:
                    # AI provided an original_filename, but it's NOT in the current input batch. Discard.
                    self.error_signal.emit(f"ResumeParser Critical Warning: AI part #{source_part_idx + 1} provided 'original_filename': '{ai_fn}', which is NOT an input file in the current batch. Discarding this AI block to prevent cross-talk.")
            else: # AI block is missing 'original_filename'
                self.error_signal.emit(f"ResumeParser: AI part #{source_part_idx + 1} is missing 'original_filename'. It will be considered for cautious ordered assignment.")
                ai_blocks_for_ordered_assignment.append(ai_block)

        # --- Secondary Ordered Assignment Pass (Very Cautious) ---
        # Get list of input files that were NOT matched by 'original_filename'
        unmatched_input_files_info = [
            fi for fi in file_info_list_for_batch if fi['filename'] not in matched_input_files_by_ai_filename
        ]

        if unmatched_input_files_info and ai_blocks_for_ordered_assignment:
            # Only proceed if counts are IDENTICAL and non-zero.
            if len(unmatched_input_files_info) == len(ai_blocks_for_ordered_assignment):
                self.error_signal.emit(
                    f"ResumeParser: Attempting ordered assignment for {len(unmatched_input_files_info)} item(s). "
                    "This relies on the AI maintaining output order for blocks that were missing 'original_filename'."
                )
                for i in range(len(unmatched_input_files_info)):
                    target_file_info = unmatched_input_files_info[i]
                    ai_block_for_assignment = ai_blocks_for_ordered_assignment[i]
                    resume_data_from_ai = ai_block_for_assignment['data']
                    source_part_idx = ai_block_for_assignment['source_part_index']

                    target_filename = target_file_info['filename']
                    self.error_signal.emit(
                        f"ResumeParser: Assigning AI response part #{source_part_idx + 1} (which was missing 'original_filename') "
                        f"to input file '{target_filename}' based on strict order and count match."
                    )

                    details = filename_to_details_map[target_filename]
                    processed_data = self.process_resume_data(resume_data_from_ai)
                    processed_data.update(details['info_dict'])
                    processed_data['raw_text'] = details['raw_text']

                    parsed_results_by_input_filename[target_filename] = processed_data
                    # No need to add to matched_input_files_by_ai_filename here.
            else:
                self.error_signal.emit(
                    f"ResumeParser Critical Warning: Mismatch in counts for ordered assignment. "
                    f"Unmatched input files: {len(unmatched_input_files_info)}, "
                    f"AI blocks missing 'original_filename': {len(ai_blocks_for_ordered_assignment)}. "
                    "Skipping ordered assignment entirely to prevent data cross-contamination. "
                    "Unmatched input files will retain fallback data."
                )
        elif unmatched_input_files_info and not ai_blocks_for_ordered_assignment:
            self.error_signal.emit(f"ResumeParser Info: {len(unmatched_input_files_info)} input file(s) remain unmatched, and there are no AI blocks (missing 'original_filename') available for ordered assignment. These files will use fallback data.")
        elif not unmatched_input_files_info and ai_blocks_for_ordered_assignment:
             self.error_signal.emit(f"ResumeParser Info: All input files were matched by 'original_filename'. {len(ai_blocks_for_ordered_assignment)} AI block(s) that were missing 'original_filename' will be discarded as there are no unmatched input files.")

        # Assemble final_results in the original order of file_info_list_for_batch
        # using the data from parsed_results_by_input_filename.
        final_parsed_results = []
        for fi_original in file_info_list_for_batch:
            final_parsed_results.append(parsed_results_by_input_filename[fi_original['filename']])

        # Log warnings if counts of AI parts vs input files are mismatched
        num_successfully_parsed_ai_json_blocks = len(successfully_parsed_ai_blocks)
        num_input_files = len(file_info_list_for_batch)

        if num_successfully_parsed_ai_json_blocks > num_input_files:
             self.error_signal.emit(f"ResumeParser Warning: AI returned more parsable JSON blocks ({num_successfully_parsed_ai_json_blocks}) than input files ({num_input_files}). Extra AI data may have been discarded if not matched by 'original_filename' or through cautious ordered assignment.")
        elif num_successfully_parsed_ai_json_blocks < num_input_files:
             self.error_signal.emit(f"ResumeParser Warning: AI returned fewer parsable JSON blocks ({num_successfully_parsed_ai_json_blocks}) than input files ({num_input_files}). Some input files will use fallback data.")

        # Check if any files still have their initial fallback data (as a proxy for parsing failure for that file)
        num_files_with_fallback = 0
        for result_data in final_parsed_results:
            # Check for a tell-tale sign of fallback data, e.g., 'Unknown' email and name
            # and no experience, which are unlikely for a successfully parsed resume.
            if result_data.get('email') == 'Unknown' and result_data.get('full_name') == 'Unknown' and not result_data.get('experience'):
                num_files_with_fallback +=1

        if num_files_with_fallback > 0:
            self.error_signal.emit(
                f"ResumeParser Notice: {num_files_with_fallback} out of {num_input_files} input file(s) in this batch "
                "are using fallback data. This could be due to AI not returning data for them, "
                "parsing errors for specific AI blocks, or mismatches preventing assignment."
            )
        return final_parsed_results

    def process_resume_data(self, resume_data):
        """Process and validate resume data"""
        # Define field specifications: (field_name, default_value, expected_type)
        # expected_type can be 'str', 'list_str', 'list_dict', 'dict'
        field_specs = {
            'full_name': ('Unknown', 'str'),
            'email': ('Unknown', 'str'),
            'phone': ('Unknown', 'str'),
            'location': ('Unknown', 'str'),
            'security_clearance': ([], 'list_str'), # Expects list of strings, or single string
            'attributes': ([], 'list_str'),
            'objectives': ('Unknown', 'str'),
            'skills': ({'technical': [], 'soft': [], 'domain_specific': [], 'certifications_as_skills': []}, 'dict'),
            'experience': ([], 'list_dict'), # List of dictionaries
            'education': ([], 'list_dict'),  # List of dictionaries
            'certifications': ([], 'list_dict'), # List of dictionaries
            'languages': ({'programming': [], 'human': []}, 'dict'),
            'projects': ([], 'list_dict'), # List of dictionaries
            'resume_tags': ([], 'list_str') # Added resume_tags
        }

        processed_data = {}
        for field, (default_val, type_hint) in field_specs.items():
            value = resume_data.get(field)

            if value is None:
                processed_data[field] = default_val
            elif type_hint == 'str':
                processed_data[field] = str(value) if value else default_val
            elif type_hint == 'list_str': # Expects a list of strings, or a single string to be converted to list
                if isinstance(value, list):
                    processed_data[field] = [str(item) for item in value]
                elif isinstance(value, str):
                    if not value.strip(): # Handle empty string case
                         processed_data[field] = []
                    # If it looks like a JSON list, try to parse it
                    elif value.startswith('[') and value.endswith(']'):
                        try:
                            parsed_list = json.loads(value)
                            if isinstance(parsed_list, list):
                                processed_data[field] = [str(item) for item in parsed_list]
                            else: # Parsed but not a list
                                processed_data[field] = [str(parsed_list)]
                        except json.JSONDecodeError: # Not valid JSON, treat as single item list
                            processed_data[field] = [value]
                    else: # Not a JSON list string, treat as single item list
                        processed_data[field] = [value]
                else:
                    processed_data[field] = [str(value)] # Not a list or string, convert to list of string
            elif type_hint == 'list_dict': # Expects a list of dictionaries
                if isinstance(value, list):
                    # Basic check: ensure items are dicts, or try to make them
                    processed_data[field] = [item if isinstance(item, dict) else {'value': str(item)} for item in value]
                elif isinstance(value, str):
                     # If it looks like a JSON list, try to parse it
                    if value.startswith('[') and value.endswith(']'):
                        try:
                            parsed_list = json.loads(value)
                            if isinstance(parsed_list, list):
                                processed_data[field] = [item if isinstance(item, dict) else {'value': str(item)} for item in parsed_list]
                            else: # Parsed but not a list
                                processed_data[field] = [{'value': str(parsed_list)}]
                        except json.JSONDecodeError: # Not valid JSON, treat as single item list with placeholder
                            processed_data[field] = [{'value': value}]
                    else: # Not a JSON list string, treat as single item list with placeholder
                        processed_data[field] = [{'value': value}]
                else:
                    processed_data[field] = default_val # Fallback to default if type is unexpected
            elif type_hint == 'dict':
                if isinstance(value, dict):
                    processed_data[field] = value
                elif isinstance(value, str):
                    # If it looks like a JSON dict, try to parse it
                    if value.startswith('{') and value.endswith('}'):
                        try:
                            parsed_dict = json.loads(value)
                            if isinstance(parsed_dict, dict):
                                processed_data[field] = parsed_dict
                            else: # Parsed but not a dict
                                processed_data[field] = default_val
                        except json.JSONDecodeError: # Not valid JSON
                            processed_data[field] = default_val
                    else: # Not a JSON dict string
                        processed_data[field] = default_val
                else:
                    processed_data[field] = default_val
            else: # Should not happen
                processed_data[field] = value

        # The AI is now responsible for full_name extraction, including using filename.
        # The prompt also guides AI to avoid job titles for names.

        return processed_data

    def create_fallback_data(self):
        """Create fallback data for failed parsing"""
        return {
            'full_name': 'Unknown',
            'email': 'Unknown',
            'phone': 'Unknown',
            'location': 'Unknown',
            'security_clearance': [],
            'attributes': [],
            'objectives': 'Unknown',
            'skills': {'technical': [], 'soft': [], 'domain_specific': [], 'certifications_as_skills': []},
            'experience': [],
            'education': [],
            'certifications': [],
            'languages': {'programming': [], 'human': []},
            'projects': [],
            'resume_tags': []
        }

    def _create_fallback_data_with_file_info(self, file_info_dict, raw_text_for_db):
        """Helper method to create fallback data and include file-specific info"""
        fallback = self.create_fallback_data() # Gets generic fallback structure
        fallback.update(file_info_dict) # Adds filename, file_path, upload_date, content_hash from input
        fallback['raw_text'] = raw_text_for_db # Adds the raw text extracted from this file
        return fallback

    def extract_text_from_document(self, file_path):
        """
        Extract text from PDF, DOC, or DOCX files.
        Returns a tuple: (extracted_text, effective_file_path)
        effective_file_path is the path to the file text was extracted from (could be a converted PDF).
        """
        _, file_ext_with_dot = os.path.splitext(file_path)
        file_ext = file_ext_with_dot.lower().lstrip('.')

        if file_ext == 'pdf':
            text = self.extract_text_from_pdf(file_path)
            return text, file_path
        elif file_ext == 'docx':
            if self.auto_convert_docx_to_pdf:
                return self._convert_docx_to_pdf_and_extract_text(file_path)
            else:
                self.error_signal.emit(f"Info: DOCX to PDF conversion disabled by setting. Extracting text directly from '{os.path.basename(file_path)}'.")
                return self.extract_text_from_docx(file_path), file_path
        elif file_ext == 'doc':
            # Attempt to convert .doc to PDF and extract, similar to .docx
            return self._convert_doc_to_pdf_and_extract_text(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")

    def _convert_docx_to_pdf_and_extract_text(self, docx_file_path):
        """
        Converts a DOCX file to a PDF in the same directory, extracts text from that PDF.
        Returns (text, path_to_pdf_or_original_docx).
        If conversion fails, falls back to extracting text directly from DOCX.
        """
        base, _ = os.path.splitext(docx_file_path)
        pdf_output_path = base + ".pdf"
        original_filename = os.path.basename(docx_file_path)
        pdf_filename = os.path.basename(pdf_output_path)

        self.error_signal.emit(f"Info: DOCX file '{original_filename}' detected. Attempting conversion to '{pdf_filename}' for text extraction.")
        try:
            # Perform the conversion. docx2pdf will overwrite if pdf_output_path exists.
            # Ensure the directory for pdf_output_path exists and is writable.
            # For simplicity, we assume it's the same dir as docx_file_path.
            convert_docx_to_pdf(docx_file_path, pdf_output_path)

            if os.path.exists(pdf_output_path):
                self.error_signal.emit(f"Info: Successfully converted '{original_filename}' to '{pdf_filename}'. Extracting text from PDF.")
                # Extract text from the converted PDF
                pdf_text = self.extract_text_from_pdf(pdf_output_path)
                return pdf_text, pdf_output_path # Return text and path to NEW PDF
            else:
                # This case might occur if conversion silently failed without an exception.
                self.error_signal.emit(f"Warning: DOCX to PDF conversion for '{original_filename}' did not produce '{pdf_filename}'. Falling back to direct DOCX text extraction.")
                docx_text = self.extract_text_from_docx(docx_file_path)
                return docx_text, docx_file_path # Return original docx text and path

        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            if not error_msg and hasattr(e, 'args') and e.args: # Try to get a message if str(e) is empty
                error_msg = str(e.args[0]) if e.args else "No specific error message"

            self.error_signal.emit(f"Warning: Failed to convert DOCX '{original_filename}' to PDF (Type: {error_type}, Error: {error_msg}). Falling back to direct DOCX text extraction.")
            # Fallback to extracting text directly from the DOCX file
            docx_text = self.extract_text_from_docx(docx_file_path)
            return docx_text, docx_file_path # Return original docx text and path

    def _convert_doc_to_pdf_and_extract_text(self, doc_file_path):
        """
        Attempts to convert a DOC file to a PDF, extracts text from that PDF.
        Returns a tuple: (extracted_text, effective_file_path).
        If conversion fails, falls back to extracting text directly from DOC.
        """
        base, _ = os.path.splitext(doc_file_path)
        pdf_output_path = base + ".pdf"
        original_filename = os.path.basename(doc_file_path)
        pdf_filename = os.path.basename(pdf_output_path)

        self.error_signal.emit(f"Info: DOC file '{original_filename}' detected. Attempting conversion to '{pdf_filename}' for text extraction.")
        try:
            # Attempt conversion using docx2pdf (which might use LibreOffice/MS Word)
            convert_docx_to_pdf(doc_file_path, pdf_output_path)

            if os.path.exists(pdf_output_path):
                self.error_signal.emit(f"Info: Successfully converted '{original_filename}' to '{pdf_filename}'. Extracting text from PDF.")
                pdf_text = self.extract_text_from_pdf(pdf_output_path)
                return pdf_text, pdf_output_path # Return text and path to NEW PDF
            else:
                self.error_signal.emit(f"Warning: DOC to PDF conversion for '{original_filename}' did not produce '{pdf_filename}'. Falling back to direct DOC text extraction.")
                # Fallback to extracting text directly from the original .doc file
                doc_text = self.extract_text_from_doc(doc_file_path)
                return doc_text, doc_file_path

        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)
            if not error_msg and hasattr(e, 'args') and e.args: # Try to get a message if str(e) is empty
                error_msg = str(e.args[0]) if e.args else "No specific error message"

            self.error_signal.emit(f"Warning: Failed to convert DOC '{original_filename}' to PDF (Type: {error_type}, Error: {error_msg}). Falling back to direct DOC text extraction.")
            # Fallback to extracting text directly from the DOC file
            doc_text = self.extract_text_from_doc(doc_file_path)
            return doc_text, doc_file_path

    def _extract_text_with_libreoffice(self, file_path):
        """Attempt to extract text from a DOC file using LibreOffice/soffice."""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Command to convert DOC to TXT using LibreOffice
                # soffice --headless --convert-to txt:"Text (encoded):UTF8" --outdir /path/to/temp_dir /path/to/your.doc
                # The output filename will be the same as input but with .txt extension.
                cmd = [
                    'soffice', '--headless', '--invisible', '--convert-to',
                    'txt:Text (encoded):UTF8', '--outdir', temp_dir, file_path
                ]

                self.error_signal.emit(f"Info: Attempting LibreOffice conversion for '{os.path.basename(file_path)}'.")
                process = subprocess.run(cmd, capture_output=True, text=False, timeout=60) # Use text=False for bytes

                if process.returncode == 0:
                    output_filename_base = os.path.splitext(os.path.basename(file_path))[0]
                    converted_txt_path = os.path.join(temp_dir, output_filename_base + '.txt')

                    if os.path.exists(converted_txt_path):
                        with open(converted_txt_path, 'r', encoding='utf-8', errors='replace') as f:
                            text_content = f.read()
                        self.error_signal.emit(f"Info: Successfully extracted text using LibreOffice for '{os.path.basename(file_path)}'.")
                        return text_content.strip()
                    else:
                        self.error_signal.emit(f"Warning: LibreOffice conversion for '{os.path.basename(file_path)}' completed, but output .txt file not found at '{converted_txt_path}'.")
                        return None
                else:
                    stderr_output = process.stderr.decode('utf-8', errors='ignore').strip() if process.stderr else "No stderr."
                    stdout_output = process.stdout.decode('utf-8', errors='ignore').strip() if process.stdout else "No stdout."
                    self.error_signal.emit(f"Warning: LibreOffice conversion failed for '{os.path.basename(file_path)}'. RC: {process.returncode}. Stderr: {stderr_output}. Stdout: {stdout_output}")
                    return None
        except FileNotFoundError:
            self.error_signal.emit(f"Info: 'soffice' (LibreOffice) command not found. Skipping LibreOffice text extraction for '{os.path.basename(file_path)}'.")
            return None
        except subprocess.TimeoutExpired:
            self.error_signal.emit(f"Warning: LibreOffice conversion timed out for '{os.path.basename(file_path)}'.")
            return None
        except Exception as e:
            self.error_signal.emit(f"Error during LibreOffice text extraction for '{os.path.basename(file_path)}': {str(e)}")
            return None

    def extract_text_from_doc(self, file_path):
        """Extract text from DOC file using antiword."""
        # Removed antiword and LibreOffice attempts.
        # Proceeding directly to binary text extraction.
        self.error_signal.emit(f"Info: Attempting direct binary text extraction for DOC '{os.path.basename(file_path)}'.")
        try:
            with open(file_path, 'rb') as f:
                raw_bytes = f.read()

            for encoding in ['utf-8', 'latin-1', 'cp1252']: # Common encodings
                try:
                    decoded_text = raw_bytes.decode(encoding, errors='replace') # Use 'replace' for unmappable chars
                    # Filter out most non-printable characters, keeping common whitespace
                    cleaned_text_fragments = [char for char in decoded_text if char.isprintable() or char in '\n\r\t']
                    text = "".join(cleaned_text_fragments)

                    if len(text.strip()) > 50: # Arbitrary threshold for "substantial" text
                        self.error_signal.emit(f"Info: Successfully extracted raw text from DOC '{os.path.basename(file_path)}' using binary read (encoding: {encoding}).")
                        return text.strip()
                except UnicodeDecodeError:
                    continue # Try next encoding

            self.error_signal.emit(f"Warning: Binary read for DOC '{os.path.basename(file_path)}' did not yield substantial text after trying common encodings.")
        except Exception as binary_read_error:
            self.error_signal.emit(f"Error during binary read of DOC '{os.path.basename(file_path)}': {str(binary_read_error)}")

        # If all attempts failed
        final_error_msg = f"Failed to extract text from DOC '{os.path.basename(file_path)}' using all available methods."
        self.error_signal.emit(final_error_msg) # Emit the final error
        raise ValueError(final_error_msg) # Raise to signal failure to the calling parser loop

    def extract_text_from_pdf(self, file_path):
        """Optimized text extraction from PDF file"""
        try:
            doc = fitz.open(file_path)
            page_count = len(doc)

            # Performance optimization: limit pages for large PDFs
            max_pages = min(15, page_count) if page_count > 15 else page_count

            text_fragments = []
            total_text_length = 0

            for page_num in range(max_pages):
                page = doc[page_num]

                # Use faster extraction method based on document size
                if page_count > 10:
                    # Fast extraction for large documents
                    text = page.get_text()
                    if text.strip():
                        text_fragments.append(text)
                        total_text_length += len(text)
                else:
                    # Detailed extraction for smaller documents
                    blocks = page.get_text("blocks", sort=True)
                    page_text = []

                    for b in blocks:
                        if b[6] == 0:  # 0 for text block
                            page_text.append(b[4])

                    # Only extract annotations for smaller documents
                    for annot in page.annots():
                        content = annot.info.get("content", "")
                        if content and content.strip():
                            page_text.append(content)

                        if annot.type[0] == fitz.PDF_ANNOT_WIDGET:
                            widget_text_val = annot.widget_text()
                            if widget_text_val and widget_text_val.strip():
                                page_text.append(widget_text_val)

                    if page_text:
                        combined_text = "\n".join(page_text)
                        text_fragments.append(combined_text)
                        total_text_length += len(combined_text)

                # Early termination if we have enough content (50KB)
                if total_text_length > 50000:
                    break

            doc.close()

            # Log performance info for large documents
            if page_count > max_pages:
                self.error_signal.emit(f"Performance: PDF '{os.path.basename(file_path)}' has {page_count} pages, processed first {max_pages} for speed.")

            # Remove duplicates and clean up
            if text_fragments:
                unique_text_fragments = list(dict.fromkeys(text_fragments))
                cleaned_fragments = [str(frag) for frag in unique_text_fragments if frag and str(frag).strip()]
                return "\n".join(cleaned_fragments)
            return ""
        except Exception as e:
            detailed_error_msg = (
                f"Error extracting text from PDF '{os.path.basename(file_path)}' with PyMuPDF: {str(e)}. "
                "Possible causes: File is corrupted, password-protected (not supported), not a valid PDF, "
                f"or path/permissions issue. Problematic file path: {file_path}"
            )
            self.error_signal.emit(detailed_error_msg)
            raise  # Re-raise to be caught by the caller for skipping

    def extract_text_from_docx(self, file_path):
        """Extract text from DOCX file"""
        all_text_fragments = []
        try:
            doc = docx.Document(file_path)

            # Extract text from paragraphs
            for para in doc.paragraphs:
                all_text_fragments.append(para.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        all_text_fragments.append(cell.text)

            # Extract text from headers and footers
            for section in doc.sections:
                # Primary Header
                if section.header:
                    for para in section.header.paragraphs:
                        all_text_fragments.append(para.text)
                    for table_in_header in section.header.tables:
                        for row in table_in_header.rows:
                            for cell in row.cells:
                                all_text_fragments.append(cell.text)
                # Primary Footer
                if section.footer:
                    for para in section.footer.paragraphs:
                        all_text_fragments.append(para.text)
                    for table_in_footer in section.footer.tables:
                        for row in table_in_footer.rows:
                            for cell in row.cells:
                                all_text_fragments.append(cell.text)

            # Supplemental XML extraction to catch text in other places like some text boxes
            try:
                # doc.part.element is the <w:document> lxml element.
                # .xpath('.//w:t') finds all <w:t> (text run) elements within the document.
                for t_element in doc.part.element.xpath('.//w:t'):
                    if t_element.text:
                        all_text_fragments.append(t_element.text)
            except AttributeError: # Fallback if doc.part.element is not as expected
                try:
                    for t_element in doc.element.body.xpath('.//w:t'): # Try on <w:body>
                        if t_element.text:
                            all_text_fragments.append(t_element.text)
                except Exception as e_body_xpath:
                    self.error_signal.emit(f"Warning: Supplemental XML text extraction (body) failed for DOCX {os.path.basename(file_path)}: {str(e_body_xpath)}")
            except Exception as e_xpath:
                self.error_signal.emit(f"Warning: Supplemental XML text extraction (part) failed for DOCX {os.path.basename(file_path)}: {str(e_xpath)}")

            # Remove duplicate fragments while preserving order, and filter out empty/whitespace-only strings
            if all_text_fragments:
                unique_text_fragments = list(dict.fromkeys(all_text_fragments))
                cleaned_fragments = [str(frag) for frag in unique_text_fragments if frag and str(frag).strip()]
                return "\n".join(cleaned_fragments)
            return ""
        except Exception as e:
            self.error_signal.emit(f"Error extracting text from DOCX {os.path.basename(file_path)}: {str(e)}")
            raise # Re-raise to be caught by the caller

    def _calculate_sha256(self, text):
        """Helper function to calculate SHA256 hash of a string."""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()


class BasicResumeProcessor(QThread):
    """Thread for basic processing of resumes (text extraction only, no AI)."""
    progress_signal = pyqtSignal(int)
    complete_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)

    def __init__(self, file_paths, db_manager, auto_convert_docx_to_pdf=False): # Added auto_convert_docx_to_pdf
        super().__init__()
        self.file_paths = file_paths
        self.db_manager = db_manager
        self.auto_convert_docx_to_pdf = auto_convert_docx_to_pdf

    def _basic_extraction_worker(self, file_path):
        """Worker function for concurrent basic text extraction."""
        try:
            current_filename = os.path.basename(file_path)
            temp_parser = ResumeParser([], "", self.db_manager, self.auto_convert_docx_to_pdf)
            temp_parser.error_signal.connect(self.error_signal)

            text, effective_file_path = temp_parser.extract_text_from_document(file_path)
            effective_file_path_for_storage = transform_path_for_storage(effective_file_path)
            content_hash = temp_parser._calculate_sha256(text)
            current_filename = os.path.basename(effective_file_path)

            return {
                'file_path': effective_file_path_for_storage,
                'filename': current_filename,
                'text': text,
                'content_hash': content_hash,
                'error': None
            }
        except Exception as e:
            return {
                'file_path': file_path,
                'filename': os.path.basename(file_path),
                'text': None,
                'content_hash': None,
                'error': str(e)
            }

    def run(self):
        total_files = len(self.file_paths)
        if total_files == 0:
            self.complete_signal.emit([])
            return

        # Get existing resume identifiers for duplicate checking
        try:
            existing_resumes_identifiers = self.db_manager.get_resume_hashes_and_filenames()
            existing_hashes = {item['content_hash'] for item in existing_resumes_identifiers if item['content_hash']}
            existing_filenames = {item['filename'] for item in existing_resumes_identifiers}
        except Exception as e:
            self.error_signal.emit(f"Error fetching existing resume identifiers: {str(e)}. Proceeding with less robust duplicate check.")
            existing_hashes = set()
            existing_filenames = set()

        # Use concurrent processing for basic extraction
        num_cores = os.cpu_count() or 1

        # Scale workers for basic processing (less intensive than AI processing)
        if total_files <= 50:
            max_workers = min(6, num_cores)
        elif total_files <= 200:
            max_workers = min(12, num_cores * 2)
        else:
            max_workers = min(20, num_cores * 3)

        num_workers = min(max_workers, total_files)
        self.error_signal.emit(f"Basic processing using {num_workers} worker threads for {total_files} files")

        extracted_results = []
        processed_count = 0

        # Phase 1: Concurrent text extraction
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
            future_to_path = {executor.submit(self._basic_extraction_worker, fp): fp for fp in self.file_paths}

            for future in concurrent.futures.as_completed(future_to_path):
                try:
                    result = future.result()
                    extracted_results.append(result)
                except Exception as exc:
                    filepath_for_error = future_to_path[future]
                    self.error_signal.emit(f"Unhandled exception for {os.path.basename(filepath_for_error)}: {exc}")
                    extracted_results.append({
                        'file_path': filepath_for_error,
                        'filename': os.path.basename(filepath_for_error),
                        'text': None,
                        'content_hash': None,
                        'error': str(exc)
                    })

                processed_count += 1
                progress = int((processed_count / total_files) * 100)
                self.progress_signal.emit(progress)

        # Phase 2: Process results and create fallback data
        processed_resumes = []
        batch_processed_hashes = set()

        for result in extracted_results:
            if result['error'] or result['text'] is None:
                continue

            # Duplicate checking
            content_hash = result['content_hash']
            filename = result['filename']

            if content_hash in existing_hashes or content_hash in batch_processed_hashes:
                self.error_signal.emit(f"Skipping duplicate (content match): {filename}")
                continue
            if filename in existing_filenames:
                self.error_signal.emit(f"Skipping duplicate (filename match): {filename}")
                continue

            batch_processed_hashes.add(content_hash)

            # Create file info and fallback data
            file_info = {
                'filename': filename,
                'file_path': result['file_path'],
                'upload_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'content_hash': content_hash
            }

            # Create temporary parser to use fallback data creation method
            temp_parser = ResumeParser([], "", self.db_manager, self.auto_convert_docx_to_pdf)
            fallback_data = temp_parser._create_fallback_data_with_file_info(file_info, result['text'][:50000])
            processed_resumes.append(fallback_data)

        self.complete_signal.emit(processed_resumes)


class DatabaseManager:
    """Manages SQLite database operations"""
    def __init__(self, db_path):
        self.db_path = db_path
        self.create_tables()

    @staticmethod
    def _sqlite_regexp(pattern, item):
        """Case-insensitive REGEXP function for SQLite."""
        if item is None:
            return False
        try:
            # item is converted to string to handle potential non-string data from DB
            return re.search(pattern, str(item), re.IGNORECASE) is not None
        except re.error:
            # This might happen if the pattern is malformed, though we escape user input.
            return False

    def _get_connection(self):
        """Returns a SQLite connection with the REGEXP function registered."""
        conn = sqlite3.connect(self.db_path)
        conn.create_function("REGEXP", 2, DatabaseManager._sqlite_regexp)
        return conn

    def _parse_boolean_query_to_dnf(self, query_string: str) -> tuple[list[list[str]], list[str]]:
        """
        Parses a boolean query string into a Disjunctive Normal Form (DNF) like structure.
        Output: A list of AND-groups. Each AND-group is a list of terms.
                These AND-groups are implicitly OR-ed together.
                Also returns a flat list of unique terms found in the query for highlighting.
        Example: "Java AND Spring OR Python" -> ([['Java', 'Spring'], ['Python']], ['Java', 'Spring', 'Python'])
        Example: "VIC OR QLD" -> ([['VIC'], ['QLD']], ['VIC', 'QLD'])
        Example: "VIC QLD" -> ([['VIC', 'QLD']], ['VIC', 'QLD'])
        """
        # Stage 1: Use shlex to handle quoted strings and initial tokenization
        try:
            # shlex.split handles quoted phrases correctly as single tokens
            # e.g., '"Project management" and skill' -> ['Project management', 'and', 'skill']
            initial_shlex_tokens = shlex.split(query_string.strip())
        except ValueError: # Handles unclosed quotes
            # Fallback: treat the whole string as a single term if shlex fails
            term = query_string.strip()
            if term:
                return [[term]], [term]
            return [], []

        if not initial_shlex_tokens:
            return [], []

        # Stage 2: Refine tokens from shlex.
        # Merge consecutive non-keyword tokens (which shlex might have split if unquoted)
        # into single phrase terms. Keywords "AND", "OR" are preserved.
        refined_tokens_for_dnf = []
        phrase_buffer = []
        for shlex_token in initial_shlex_tokens:
            shlex_token_upper = shlex_token.upper()
            if shlex_token_upper == "AND" or shlex_token_upper == "OR":
                if phrase_buffer: # Finalize any pending phrase from unquoted words
                    refined_tokens_for_dnf.append(" ".join(phrase_buffer))
                    phrase_buffer = []
                refined_tokens_for_dnf.append(shlex_token_upper) # Add "AND" or "OR" as uppercase
            else:
                # This token is part of a term. It could be:
                # 1. A single word from an unquoted sequence (e.g., "Project" from "Project management").
                # 2. A full phrase if it was quoted in the input (e.g., "Spring Boot" from "\"Spring Boot\"").
                phrase_buffer.append(shlex_token) # Append original case token

        if phrase_buffer: # Append any remaining phrase at the end
            refined_tokens_for_dnf.append(" ".join(phrase_buffer))

        # Stage 3: Construct DNF from the refined tokens
        if not refined_tokens_for_dnf:
            return [], []

        or_groups = []
        current_and_group = []
        all_actual_terms_set = set() # Use a set for unique terms

        for token in refined_tokens_for_dnf: # Iterate through our refined tokens
            # `token` is now either an actual term string (possibly a phrase) or "AND"/"OR"
            if token == "OR": # Keywords are already uppercase
                if current_and_group:
                    or_groups.append(current_and_group)
                current_and_group = []
            elif token == "AND":
                # "AND" is an implicit separator for terms within an AND group.
                # Terms are added to current_and_group when they are encountered.
                pass
            else: # It's a term
                current_and_group.append(token)
                all_actual_terms_set.add(token)

        if current_and_group:
            or_groups.append(current_and_group)

        unique_sorted_terms = sorted(list(all_actual_terms_set))
        return or_groups, unique_sorted_terms

    def create_tables(self):
        """Create database tables if they don't exist"""
        conn = self._get_connection()
        cursor = conn.cursor()

        # Create resumes table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS resumes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            upload_date TEXT NOT NULL,
            content_hash TEXT UNIQUE  -- Added for duplicate detection
        )
        ''')

        # Create resume_data table with enhanced fields
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS resume_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            resume_id INTEGER NOT NULL,
            full_name TEXT,
            email TEXT,
            phone TEXT,
            location TEXT,
            security_clearance TEXT,
            attributes TEXT,
            objectives TEXT,
            skills TEXT,
            experience TEXT,
            education TEXT,
            certifications TEXT,
            languages TEXT,
            projects TEXT,
            raw_text TEXT,
            tags TEXT,
            FOREIGN KEY (resume_id) REFERENCES resumes (id)
        )
        ''')

        conn.commit()
        conn.close()

        self._ensure_search_indexes() # Ensure regular indexes exist


    def save_resume_data(self, resume_data_list):
        """Save parsed resume data to database"""
        conn = self._get_connection()
        cursor = conn.cursor()

        for data in resume_data_list:
            # Insert into resumes table
            cursor.execute('''
            INSERT INTO resumes (filename, file_path, upload_date, content_hash)
            VALUES (?, ?, ?, ?)
            ''', (data['filename'], data['file_path'], data['upload_date'], data.get('content_hash', None)))

            resume_id = cursor.lastrowid

            # Define field categories for serialization
            # Fields that are lists but should be stored as single string if one item, or JSON array if multiple
            single_item_list_fields = ['security_clearance', 'attributes']
            # Fields that are complex types (lists/dicts) and should always be stored as JSON strings
            json_complex_fields = ['skills', 'experience', 'education',
                                   'certifications', 'languages', 'projects', 'resume_tags']

            # Process single_item_list_fields
            for field in single_item_list_fields:
                value = data.get(field) # Expected to be a list from ResumeParser
                if isinstance(value, list):
                    if len(value) == 1:
                        data[field] = str(value[0])
                    elif len(value) == 0:
                        data[field] = ""  # Store empty list as empty string
                    else:
                        data[field] = json.dumps(value) # Store multiple items as JSON list string
                elif value is None: # Field not present or explicitly None
                    data[field] = ""
                # If value is already a string, assume it's correctly formatted by parser or AI
                # ResumeParser.process_resume_data for these types ensures they are lists.

            # Process json_complex_fields
            for field in json_complex_fields:
                value = data.get(field) # Expected to be a list or dict from ResumeParser
                if value is not None:
                    data[field] = json.dumps(value)
                else: # Field not present or explicitly None, store empty JSON representation
                    if field in ['skills', 'languages']: # These are dicts
                        data[field] = json.dumps({})
                    else: # Others are lists
                        data[field] = json.dumps([])

            # Simple text fields like 'full_name', 'email', 'phone', 'location', 'objectives', 'raw_text'
            # are generally handled by data.get(field, default_value) in the INSERT statement.
            # ResumeParser ensures these are strings.

            # Insert into resume_data table with enhanced fields
            cursor.execute('''
            INSERT INTO resume_data (
                resume_id, full_name, email, phone, location, security_clearance,
                attributes, objectives, skills, experience, education, certifications,
                languages, projects, raw_text, tags
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                resume_id,
                data.get('full_name', 'Unknown'),
                data.get('email', 'Unknown'),
                data.get('phone', 'Unknown'),
                data.get('location', 'Unknown'),
                data.get('security_clearance', ""), # Now defaults to empty string
                data.get('attributes', ""),          # Now defaults to empty string
                data.get('objectives', ''),
                data.get('skills', json.dumps({})), # Default to empty JSON object string
                data.get('experience', json.dumps([])), # Default to empty JSON array string
                data.get('education', json.dumps([])),
                data.get('certifications', json.dumps([])),
                data.get('languages', json.dumps({})),
                data.get('projects', json.dumps([])),
                data.get('raw_text', ''),
                data.get('resume_tags', json.dumps([])) # Added tags
            ))

        conn.commit()
        conn.close()

    def _ensure_search_indexes(self):
        """Ensure comprehensive indexes exist for optimal search performance."""
        conn = self._get_connection()
        cursor = conn.cursor()

        # Enable WAL mode for better concurrent access
        cursor.execute("PRAGMA journal_mode=WAL;")

        # Optimize SQLite settings for performance
        cursor.execute("PRAGMA synchronous=NORMAL;")
        cursor.execute("PRAGMA cache_size=10000;")  # 10MB cache
        cursor.execute("PRAGMA temp_store=MEMORY;")
        cursor.execute("PRAGMA mmap_size=268435456;")  # 256MB memory map

        # Create comprehensive indexes for fast searching
        indexes_to_create = [
            # Primary search indexes
            "CREATE INDEX IF NOT EXISTS idx_resume_data_full_name ON resume_data(full_name COLLATE NOCASE);",
            "CREATE INDEX IF NOT EXISTS idx_resume_data_email ON resume_data(email COLLATE NOCASE);",
            "CREATE INDEX IF NOT EXISTS idx_resume_data_location ON resume_data(location COLLATE NOCASE);",
            "CREATE INDEX IF NOT EXISTS idx_resume_data_security_clearance ON resume_data(security_clearance COLLATE NOCASE);",

            # Composite indexes for common search patterns
            "CREATE INDEX IF NOT EXISTS idx_resume_data_location_clearance ON resume_data(location, security_clearance);",
            "CREATE INDEX IF NOT EXISTS idx_resume_data_name_location ON resume_data(full_name, location);",

            # Resume table indexes
            "CREATE INDEX IF NOT EXISTS idx_resumes_filename ON resumes(filename COLLATE NOCASE);",
            "CREATE INDEX IF NOT EXISTS idx_resumes_upload_date ON resumes(upload_date);",
            "CREATE INDEX IF NOT EXISTS idx_resumes_content_hash ON resumes(content_hash);",

            # Foreign key index for joins
            "CREATE INDEX IF NOT EXISTS idx_resume_data_resume_id ON resume_data(resume_id);",
        ]

        for index_sql in indexes_to_create:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                print(f"Warning: Could not create index: {e}")

        conn.commit()
        conn.close()

    def _create_fts_table(self, cursor): # This method will be removed effectively
        """Create FTS5 virtual table for high-performance text search."""
        try:
            # Drop existing FTS table if it exists
            cursor.execute("DROP TABLE IF EXISTS resume_fts;")

            # Create FTS5 virtual table
            cursor.execute("""
                CREATE VIRTUAL TABLE resume_fts USING fts5(
                    resume_id UNINDEXED,
                    full_name,
                    raw_text,
                    tags,
                    skills,
                    experience,
                    education,
                    certifications,
                    location,
                    security_clearance,
                    content='resume_data',
                    content_rowid='resume_id'
                );
            """)

            # Create triggers to keep FTS table in sync
            cursor.execute("""
                CREATE TRIGGER resume_fts_insert AFTER INSERT ON resume_data BEGIN
                    INSERT INTO resume_fts(
                        resume_id, full_name, raw_text, tags, skills,
                        experience, education, certifications, location, security_clearance
                    ) VALUES (
                        new.resume_id, new.full_name, new.raw_text, new.tags, new.skills,
                        new.experience, new.education, new.certifications, new.location, new.security_clearance
                    );
                END;
            """)

            cursor.execute("""
                CREATE TRIGGER resume_fts_delete AFTER DELETE ON resume_data BEGIN
                    INSERT INTO resume_fts(resume_fts, resume_id, full_name, raw_text, tags, skills,
                                         experience, education, certifications, location, security_clearance)
                    VALUES('delete', old.resume_id, old.full_name, old.raw_text, old.tags, old.skills,
                           old.experience, old.education, old.certifications, old.location, old.security_clearance);
                END;
            """)

            cursor.execute("""
                CREATE TRIGGER resume_fts_update AFTER UPDATE ON resume_data BEGIN
                    INSERT INTO resume_fts(resume_fts, resume_id, full_name, raw_text, tags, skills,
                                         experience, education, certifications, location, security_clearance)
                    VALUES('delete', old.resume_id, old.full_name, old.raw_text, old.tags, old.skills,
                           old.experience, old.education, old.certifications, old.location, old.security_clearance);
                    INSERT INTO resume_fts(
                        resume_id, full_name, raw_text, tags, skills,
                        experience, education, certifications, location, security_clearance
                    ) VALUES (
                        new.resume_id, new.full_name, new.raw_text, new.tags, new.skills,
                        new.experience, new.education, new.certifications, new.location, new.security_clearance
                    );
                END;
            """)

            # Populate FTS table with existing data
            cursor.execute("""
                INSERT INTO resume_fts(
                    resume_id, full_name, raw_text, tags, skills,
                    experience, education, certifications, location, security_clearance
                )
                SELECT
                    resume_id, full_name, raw_text, tags, skills,
                    experience, education, certifications, location, security_clearance
                FROM resume_data;
            """)

        except Exception as e:
            print(f"Warning: Could not create FTS table: {e}")
            # Continue without FTS if it fails

    def search_resumes(self, search_keywords: list):
        """
        Search resumes based on a list of AI-generated keywords.
        Uses SQL LIKE to filter across raw_text, tags, location, and security_clearance.
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        if not search_keywords:
            conn.close()
            return []

        # Build the WHERE clause for SQL LIKE
        # We will search raw_text, tags, location, and security_clearance
        # This will be an OR condition for each keyword across these fields.
        conditions = []
        params = []
        for kw in search_keywords:
            like_pattern = f"%{kw}%"
            conditions.append("rd.raw_text LIKE ?")
            conditions.append("rd.tags LIKE ?")
            conditions.append("rd.location LIKE ?")
            conditions.append("rd.security_clearance LIKE ?")
            params.extend([like_pattern] * 4)

        where_clause = " OR ".join(conditions)

        sql_filter = f'''
        SELECT r.id, r.filename, r.upload_date,
               rd.full_name, rd.email, rd.phone, rd.location,
               rd.security_clearance, rd.attributes, rd.objectives,
               rd.skills, rd.experience, rd.education, rd.certifications,
               rd.languages, rd.projects, rd.raw_text, rd.tags
        FROM resumes r
        JOIN resume_data rd ON r.id = rd.resume_id
        WHERE {where_clause}
        '''

        cursor.execute(sql_filter, params)
        candidate_results = cursor.fetchall()
        conn.close()

        if not candidate_results:
            return []

        # Step 2: Calculate "match count" in Python for prioritization.
        # The SQL LIKE search was broad; this Python step refines by counting keyword matches in 'tags'.
        scored_results = []
        for res_tuple in candidate_results:
            tags_json_str = res_tuple[17]
            match_count = 0
            try:
                resume_tags_list = json.loads(tags_json_str) if tags_json_str else []
                for ai_keyword in search_keywords:
                    if any(ai_keyword.lower() in tag.lower() for tag in resume_tags_list): # Case-insensitive check
                        match_count += 1
            except json.JSONDecodeError:
                pass # Ignore if tags field is malformed for this resume
            scored_results.append({'data': res_tuple, 'match_count': match_count})

        # Sort by match_count (descending), then by upload_date (descending)
        scored_results.sort(key=lambda x: (x['match_count'], datetime.datetime.strptime(x['data'][2], "%Y-%m-%d %H:%M:%S")), reverse=True)

        return [item['data'] for item in scored_results]

    def search_resumes_boolean(self, query_string: str, search_raw_text_exclusively: bool = False) -> tuple[list, list[str]]:
        """
        Search resumes based on a boolean keyword query.
        Returns a tuple: (list_of_results, list_of_unique_terms_for_highlighting)
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        parsed_dnf_query, unique_terms_for_highlight = self._parse_boolean_query_to_dnf(query_string)

        if not parsed_dnf_query:
            conn.close()
            return [], unique_terms_for_highlight # Return empty results but potentially terms if query was just "term"

        or_sql_conditions = []
        all_sql_params = []

        target_columns = ["rd.raw_text"] if search_raw_text_exclusively else ["rd.raw_text", "rd.tags", "rd.location", "rd.security_clearance"]

        for and_group in parsed_dnf_query: # e.g., and_group = ['Java', 'Spring']
            and_sql_conditions_for_this_group = []
            for term in and_group:
                clean_term = term.strip()
                if not clean_term:
                    continue

                pattern = r'\b' + re.escape(clean_term) + r'\b' # Pattern for REGEXP (whole word)

                # Conditions for this single term across multiple columns (OR-ed)
                term_col_conditions = []
                for col_name in target_columns:
                    term_col_conditions.append(f"{col_name} REGEXP ?") # Use REGEXP, removed TRIM
                    all_sql_params.append(pattern)

                if term_col_conditions:
                    # SQL for one term: ( (trim(col1) REGEXP term) OR (trim(col2) REGEXP term) ... )
                    single_term_sql_condition = f"({ ' OR '.join(term_col_conditions) })"
                    and_sql_conditions_for_this_group.append(single_term_sql_condition)

            if and_sql_conditions_for_this_group:
                # Join conditions for terms within an AND-group with "AND"
                # SQL for one AND-group: ( (term1_cond) AND (term2_cond) ... )
                or_sql_conditions.append(f"({ ' AND '.join(and_sql_conditions_for_this_group) })")

        if not or_sql_conditions:
            conn.close()
            return [], unique_terms_for_highlight

        # Join all OR-group conditions with "OR"
        # Final WHERE: ( (group1_cond) OR (group2_cond) ... )
        where_clause = " OR ".join(or_sql_conditions)

        final_query = f'''
            SELECT r.id, r.filename, r.upload_date,
                   rd.full_name, rd.email, rd.phone, rd.location,
                   rd.security_clearance, rd.attributes, rd.objectives,
                   rd.skills, rd.experience, rd.education, rd.certifications,
                   rd.languages, rd.projects, rd.raw_text, rd.tags
            FROM resume_data rd
            JOIN resumes r ON rd.resume_id = r.id
            WHERE {where_clause}
            ORDER BY r.upload_date DESC
        '''

        # FTS5 search path removed, directly use REGEXP.
        # The _search_with_fts5 method is also removed.

        # Fallback to original REGEXP search if FTS5 fails
        try:
            cursor.execute(final_query, all_sql_params)
            results = cursor.fetchall()
        except sqlite3.Error as e:
            print(f"SQL error in boolean search: {e}. Query: '{final_query}', Params: {all_sql_params}")
            results = [] # Return empty on SQL error
        finally:
            conn.close()
        return results, unique_terms_for_highlight

    def get_all_resumes(self):
        """Get all resumes from database"""
        conn = self._get_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT r.id, r.filename, r.upload_date,
               rd.full_name, rd.email, rd.phone, rd.location,
               rd.security_clearance, rd.attributes, rd.objectives,
               rd.skills, rd.experience, rd.education, rd.certifications,
               rd.languages, rd.projects, rd.raw_text, rd.tags
        FROM resumes r
        JOIN resume_data rd ON r.id = rd.resume_id
        ''')

        results = cursor.fetchall()
        conn.close()

        return results

    def get_resume_hashes_and_filenames(self):
        """Get all content hashes and filenames from the resumes table."""
        conn = self._get_connection()
        # Return results as dictionaries
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute('''
        SELECT filename, content_hash FROM resumes
        ''')

        results = cursor.fetchall()
        conn.close()
        # Convert Row objects to dictionaries
        return [dict(row) for row in results]

    def get_unique_tags(self):
        """Get all unique tags from the resume_data table."""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT tags FROM resume_data WHERE tags IS NOT NULL AND tags != ''")
            all_tags_json_strings = cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Database error fetching tags: {e}") # Simple print for now
            return []
        finally:
            conn.close()

        unique_tags = set()
        for tags_json_tuple in all_tags_json_strings:
            tags_json_str = tags_json_tuple[0]
            if tags_json_str:
                try:
                    tags_list = json.loads(tags_json_str)
                    if isinstance(tags_list, list):
                        for tag in tags_list:
                            if isinstance(tag, str) and tag.strip():
                                unique_tags.add(tag.strip().lower()) # Normalize
                except json.JSONDecodeError:
                    pass
        return sorted(list(unique_tags))

    def clear_all_data(self):
        """Clear all data from resumes and resume_data tables."""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("DELETE FROM resume_data")
            cursor.execute("DELETE FROM resumes")
            # Reset autoincrement counters for SQLite
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='resume_data'")
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='resumes'")
            conn.commit()
            return True, "Database cleared successfully."
        except sqlite3.Error as e:
            conn.rollback()
            return False, f"Error clearing database: {str(e)}"
        finally:
            conn.close()

    def get_resume_details_by_id(self, resume_id):
        """Get full details of a single resume by its ID, including file_path."""
        conn = self._get_connection()
        cursor = conn.cursor()
        # Ensure r.file_path is included for the preview function in the dialog
        cursor.execute('''
        SELECT r.id, r.filename, r.upload_date,
               rd.full_name, rd.email, rd.phone, rd.location,
               rd.security_clearance, rd.attributes, rd.objectives,
               rd.skills, rd.experience, rd.education, rd.certifications,
               rd.languages, rd.projects, rd.raw_text, rd.tags,
               r.file_path
        FROM resumes r
        JOIN resume_data rd ON r.id = rd.resume_id
        WHERE r.id = ?
        ''', (resume_id,))
        result = cursor.fetchone() # This is a tuple
        conn.close()
        return result # Returns a tuple or None


    def get_resume_file_path(self, resume_id):
        """Get the file path of a resume by its ID"""
        conn = self._get_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT file_path FROM resumes WHERE id = ?
        ''', (resume_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return result[0]
        else:
            return None

    def update_resume_basic_details(self, resume_id, data_to_update):
        """Update basic details for a given resume_id in the resume_data table."""
        conn = self._get_connection()
        cursor = conn.cursor()

        fields_to_set = []
        params = []

        allowed_fields = ['full_name', 'email', 'phone', 'location', 'security_clearance']

        for field, value in data_to_update.items():
            if field not in allowed_fields:
                continue # Skip unknown fields

            if field == 'security_clearance':
                # Handle security_clearance: store as JSON list string if multiple, else plain string
                if isinstance(value, str):
                    if ',' in value: # User might have typed comma-separated values
                        items = [s.strip() for s in value.split(',') if s.strip()]
                        if len(items) > 1:
                            processed_value = json.dumps(items)
                        elif len(items) == 1:
                            processed_value = items[0]
                        else:
                            processed_value = "" # Empty if only commas or whitespace
                    else: # Single value or empty
                        processed_value = value.strip()
                elif isinstance(value, list): # Should not happen from QLineEdit, but good to handle
                    processed_value = json.dumps(value) if value else ""
                else:
                    processed_value = ""
                fields_to_set.append(f"{field} = ?")
                params.append(processed_value)
            else: # For full_name, email, phone, location
                fields_to_set.append(f"{field} = ?")
                params.append(str(value).strip() if value is not None else "")

        if not fields_to_set:
            conn.close()
            return False, "No valid fields provided for update."

        sql = f"UPDATE resume_data SET {', '.join(fields_to_set)} WHERE resume_id = ?"
        params.append(resume_id)

        try:
            cursor.execute(sql, params)
            conn.commit()
            updated_rows = cursor.rowcount
            conn.close()
            if updated_rows > 0:
                return True, "Resume details updated successfully."
            else:
                return False, "Resume ID not found or no changes made."
        except sqlite3.Error as e:
            conn.rollback()
            conn.close()
            return False, f"Database error updating resume: {str(e)}"

    def update_full_resume_data(self, resume_id, parsed_data):
        """Update all parsed fields for a given resume_id in the resume_data table."""
        conn = self._get_connection()
        cursor = conn.cursor()

        # Prepare data for update, similar to save_resume_data
        data_for_update = parsed_data.copy() # Work on a copy

        # Define field categories for serialization
        single_item_list_fields = ['security_clearance', 'attributes']
        json_complex_fields = ['skills', 'experience', 'education',
                               'certifications', 'languages', 'projects', 'resume_tags'] # 'resume_tags' is important here

        for field in single_item_list_fields:
            value = data_for_update.get(field)
            if isinstance(value, list):
                if len(value) == 1:
                    data_for_update[field] = str(value[0])
                elif len(value) == 0:
                    data_for_update[field] = ""
                else:
                    data_for_update[field] = json.dumps(value)
            elif value is None:
                data_for_update[field] = ""

        for field in json_complex_fields:
            value = data_for_update.get(field)
            if value is not None:
                data_for_update[field] = json.dumps(value)
            else:
                if field in ['skills', 'languages']:
                    data_for_update[field] = json.dumps({})
                else:
                    data_for_update[field] = json.dumps([])

        # Fields to update in resume_data table
        # Note: raw_text is assumed to be already present from basic upload and not re-parsed here.
        # If raw_text could change or needs to be re-saved, it should be included.
        # For now, we focus on fields populated by AI.
        update_fields_sql = '''
            full_name = ?, email = ?, phone = ?, location = ?, security_clearance = ?,
            attributes = ?, objectives = ?, skills = ?, experience = ?, education = ?,
            certifications = ?, languages = ?, projects = ?, tags = ?
        '''
        # Parameters must match the order in update_fields_sql
        params = (
            data_for_update.get('full_name', 'Unknown'),
            data_for_update.get('email', 'Unknown'),
            data_for_update.get('phone', 'Unknown'),
            data_for_update.get('location', 'Unknown'),
            data_for_update.get('security_clearance', ""),
            data_for_update.get('attributes', ""),
            data_for_update.get('objectives', ''),
            data_for_update.get('skills', json.dumps({})),
            data_for_update.get('experience', json.dumps([])),
            data_for_update.get('education', json.dumps([])),
            data_for_update.get('certifications', json.dumps([])),
            data_for_update.get('languages', json.dumps({})),
            data_for_update.get('projects', json.dumps([])),
            data_for_update.get('resume_tags', json.dumps([])), # Ensure 'tags' in DB matches 'resume_tags' from parser
            resume_id # For the WHERE clause
        )

        sql = f"UPDATE resume_data SET {update_fields_sql} WHERE resume_id = ?"

        try:
            cursor.execute(sql, params)
            conn.commit()
            updated_rows = cursor.rowcount
            conn.close()
            if updated_rows > 0:
                return True, "Resume details fully updated successfully."
            else:
                return False, "Resume ID not found or no changes made during full update."
        except sqlite3.Error as e:
            conn.rollback()
            conn.close()
            return False, f"Database error during full resume update: {str(e)}"

    def get_resumes_for_data_correction(self):
        """Fetches resume_id, phone, location, and raw_text for all resumes."""
        conn = self._get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT rd.resume_id, rd.phone, rd.location, rd.raw_text
            FROM resume_data rd
            WHERE rd.full_name IS NOT NULL AND rd.full_name != 'Unknown' AND rd.full_name != ''
                  AND TRIM(rd.full_name) != ''
        ''')
        results = cursor.fetchall()
        conn.close()
        return results

    def update_corrected_phone_location(self, resume_id, corrected_phone, corrected_location):
        """Updates only the phone and location for a given resume_id."""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("UPDATE resume_data SET phone = ?, location = ? WHERE resume_id = ?",
                           (corrected_phone, corrected_location, resume_id))
            conn.commit()
        except sqlite3.Error as e:
            conn.rollback()
            # Consider logging this error or signaling it
        finally:
            conn.close()

    def delete_resumes_by_ids(self, resume_ids: list):
        """Deletes resumes and their associated data by a list of IDs."""
        if not resume_ids:
            return False, "No resume IDs provided for deletion."

        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            conn.execute("BEGIN TRANSACTION")

            # Create placeholders for the IN clause
            placeholders = ','.join(['?'] * len(resume_ids))

            # Delete from resume_data first due to foreign key constraint
            sql_delete_data = f"DELETE FROM resume_data WHERE resume_id IN ({placeholders})"
            cursor.execute(sql_delete_data, resume_ids) # FTS table and its triggers are removed.

            # deleted_data_rows = cursor.rowcount # Optional: track rows deleted from each table

            # Delete from resumes
            sql_delete_resumes = f"DELETE FROM resumes WHERE id IN ({placeholders})"
            cursor.execute(sql_delete_resumes, resume_ids)
            deleted_resumes_rows = cursor.rowcount

            conn.commit()

            return True, f"Successfully deleted {deleted_resumes_rows} resume(s) and their associated data."
        except sqlite3.Error as e:
            conn.rollback()
            return False, f"Database error during deletion: {str(e)}"
        finally:
            conn.close()

    def optimize_database(self):
        """Perform comprehensive database optimization for better performance."""
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            print("Starting database optimization...")

            # Update table statistics for query planner
            cursor.execute("ANALYZE;")


            # Vacuum database to reclaim space and optimize
            cursor.execute("VACUUM;")
            print("Database vacuumed")

            # Re-create indexes to ensure optimal performance
            self._ensure_search_indexes()

            conn.commit()
            print("Database optimization completed successfully")
            return True, "Database optimization completed successfully"

        except Exception as e:
            print(f"Database optimization failed: {e}")
            return False, f"Database optimization failed: {str(e)}"
        finally:
            conn.close()

    def get_database_stats(self):
        """Get database performance statistics."""
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            stats = {}

            # Get table sizes
            cursor.execute("SELECT COUNT(*) FROM resumes")
            stats['total_resumes'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM resume_data")
            stats['total_resume_data'] = cursor.fetchone()[0]

            stats['fts_entries'] = 0 # FTS removed
            stats['fts_enabled'] = False # FTS removed

            # Get database file size
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            stats['db_size_bytes'] = page_count * page_size
            stats['db_size_mb'] = stats['db_size_bytes'] / (1024 * 1024)

            # Get index information
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
            stats['custom_indexes'] = cursor.fetchone()[0]

            return stats

        except Exception as e:
            print(f"Error getting database stats: {e}")
            return {}
        finally:
            conn.close()



class ResumeDetailWidget(QWidget): # Changed from QDialog to QWidget
    """Widget to display detailed resume information within the main GUI."""
    def __init__(self, resume_data_tuple, rank, score, explanation, main_window_ref, search_terms=None, parent=None):
        super().__init__(parent)
        self.resume_data_tuple = resume_data_tuple
        self.rank = rank
        self.score = score
        self.explanation = explanation
        self.search_terms = search_terms if search_terms else [] # Store search terms for highlighting
        self.main_window_ref = main_window_ref # Reference to MainWindow for preview_resume
        self.is_edit_mode = False

        # Unpack key data early
        self.resume_id = resume_data_tuple[0]
        self.current_full_name = str(resume_data_tuple[3])
        self.current_email = str(resume_data_tuple[4])
        self.current_phone = str(resume_data_tuple[5])
        self.raw_text_content = str(resume_data_tuple[16]) # raw_text
        self.original_filename = str(resume_data_tuple[1])
        self.file_path = resume_data_tuple[18] # Assuming file_path is the 19th element (index 18)

        self.setWindowTitle(f"Resume Details: {self.current_full_name}")
        # self.setStyleSheet(DARK_THEME) # Theme should be inherited

        # Check if this is a "basic" resume (needing on-demand parsing)
        # We call _check_if_basic_resume later in _setup_ui after main_window_ref is fully available
        # for settings access, but initialize the flag here.
        self.is_basic_resume = False

        # Initial check for very basic fields to set window title appropriately even before full check
        self.is_basic_resume = (self.current_full_name == "Unknown" and self.current_email == "Unknown" and self.current_phone == "Unknown")

        self._setup_ui()

    def _highlight_text(self, text_content, terms_to_highlight):
        if not text_content or not terms_to_highlight:
            return html.escape(str(text_content))

        # Escape the original text first to handle special HTML characters in the content
        escaped_text = html.escape(str(text_content))

        # Sort original terms by length (descending) to prioritize longer matches (e.g., "Project Manager" before "Manager")
        # This helps if the regex engine has specific behavior with overlapping patterns in `|`,
        # though for `re.sub` it usually finds the leftmost longest match first.
        sorted_original_terms = sorted(terms_to_highlight, key=len, reverse=True)

        processed_terms_for_regex = []
        for term in sorted_original_terms:
            escaped_term = re.escape(term)
            # For phrases (containing spaces), match them as literal strings.
            # For single words, use word boundaries \b to avoid partial matches (e.g., "dev" in "develop").
            if ' ' in term:
                processed_terms_for_regex.append(escaped_term)
            else:
                processed_terms_for_regex.append(r'\b' + escaped_term + r'\b')

        if not processed_terms_for_regex:
            return escaped_text

        # Create a single regex pattern to find any of the terms, case-insensitively
        pattern = re.compile(f"({'|'.join(processed_terms_for_regex)})", re.IGNORECASE)

        def highlight_match(match):
            return f'<span style="background-color: yellow; color: black;">{match.group(0)}</span>'

        return pattern.sub(highlight_match, escaped_text)

    def _try_load_json(self, json_string, default_type='list'):
        if not json_string:
            return [] if default_type == 'list' else {}
        try:
            return json.loads(json_string)
        except json.JSONDecodeError:
            # Handle cases where it might be a single string not in JSON list/dict format
            if default_type == 'list' and isinstance(json_string, str) and not (json_string.startswith('[') or json_string.startswith('{')):
                return [json_string]
            return [] if default_type == 'list' else {}

    def _format_list_as_html_bullets(self, data_list, title=""):
        if not data_list:
            return f"<p><b>{title}:</b> N/A</p>" if title else "<p>N/A</p>"

        html = f"<p><b>{title}</b></p><ul>" if title else "<ul>"
        for item in data_list:
            if isinstance(item, str):
                display_item = self._highlight_text(item, self.search_terms)
                html += f"<li>{display_item}</li>"
            elif isinstance(item, dict): # For more complex list items if needed
                item_details = [] # This part might need more granular highlighting if dict values are strings
                for k, v in item.items():
                    item_details.append(f"<b>{k.replace('_',' ').title()}:</b> {v}")
                html += f"<li>{'; '.join(item_details)}</li>"
        html += "</ul>"
        return html

    def _format_dict_as_html_bullets(self, data_dict, title=""):
        if not data_dict:
            return f"<p><b>{title}:</b> N/A</p>" if title else "<p>N/A</p>"

        html = f"<p><b>{title}</b></p><ul>" if title else "<ul>"
        for key, value in data_dict.items():
            val_str = value
            if isinstance(value, list):
                val_str = ", ".join(value) if value else "N/A"
            highlighted_val_str = self._highlight_text(val_str, self.search_terms)
            html += f"<li><b>{key.replace('_',' ').title()}:</b> {highlighted_val_str}</li>"
        html += "</ul>"
        return html

    def _add_section_widget(self, layout, title, content_widget):
        group_box = QGroupBox(title)
        group_layout = QVBoxLayout()
        group_layout.addWidget(content_widget)
        group_box.setLayout(group_layout)
        layout.addWidget(group_box)

    def _add_section_html(self, layout, title, html_content):
        group_box = QGroupBox(title)
        group_layout = QVBoxLayout()

        content_label = QLabel(html_content)
        content_label.setTextFormat(Qt.RichText) # HTML content is already rich text
        content_label.setWordWrap(True)
        content_label.setAlignment(Qt.AlignTop)

        group_layout.addWidget(content_label)
        group_box.setLayout(group_layout)
        layout.addWidget(group_box)

    def _is_field_unknown(self, value):
        return value is None or str(value).strip().lower() in ["unknown", ""]

    def _check_if_basic_resume(self):
        """Checks if the current resume data indicates it's a basic upload."""
        # Indices: 3:full_name, 4:email, 5:phone
        name_unknown = self._is_field_unknown(self.resume_data_tuple[3])
        email_unknown = self._is_field_unknown(self.resume_data_tuple[4])
        phone_unknown = self._is_field_unknown(self.resume_data_tuple[5])

        # Additionally, check if complex fields like skills or experience are empty JSON
        # This helps distinguish a truly basic upload from one where AI just couldn't find contact info.
        skills_json = self.resume_data_tuple[10]
        experience_json = self.resume_data_tuple[11]

        try:
            skills_data = json.loads(skills_json) if skills_json else {}
            is_skills_empty = not any(skills_data.values()) if isinstance(skills_data, dict) else True
        except (json.JSONDecodeError, TypeError):
            is_skills_empty = True # If malformed or not JSON, treat as empty for this check

        try:
            experience_data = json.loads(experience_json) if experience_json else []
            is_experience_empty = not experience_data if isinstance(experience_data, list) else True
        except (json.JSONDecodeError, TypeError):
            is_experience_empty = True

        # A resume is considered "basic" if name, email, AND phone are unknown,
        # AND core parsed data like skills and experience are also empty/default.
        # This is a heuristic.
        self.is_basic_resume = name_unknown and email_unknown and phone_unknown and is_skills_empty and is_experience_empty

        # Log the decision for debugging
        # self.main_window_ref.log_text.append(
        #     f"Resume ID {self.resume_id} basic check: Name_UK={name_unknown}, Email_UK={email_unknown}, Phone_UK={phone_unknown}, "
        #     f"Skills_Empty={is_skills_empty}, Exp_Empty={is_experience_empty}. Result: is_basic_resume={self.is_basic_resume}"
        # )
        return self.is_basic_resume

    def _setup_ui(self):
        main_layout = QVBoxLayout(self)

        # --- AI Explanation at the TOP ---
        if self.explanation and self.explanation != "N/A (Results not ranked by AI)":
            explanation_group_box = QGroupBox("AI Match Explanation (from Ranking)")
            explanation_layout = QVBoxLayout()

            explanation_label = QLabel()
            explanation_label.setTextFormat(Qt.RichText)
            explanation_label.setWordWrap(True)
            explanation_label.setAlignment(Qt.AlignTop)

            # Process plain text explanation for basic HTML formatting (e.g., paragraphs)
            html_explanation = self.explanation.replace("\n", "<br>")
            explanation_label.setText(self._highlight_text(html_explanation, self.search_terms)) # Highlight explanation too

            explanation_layout.addWidget(explanation_label)
            explanation_group_box.setLayout(explanation_layout)
            main_layout.addWidget(explanation_group_box)

        # --- On-Demand AI Parsing Section (if basic resume) ---
        self.is_basic_resume = self._check_if_basic_resume() # Determine if it's a basic resume

        self.on_demand_parse_group = QGroupBox("Complete Candidate Details & Parse with AI")
        on_demand_main_layout = QVBoxLayout() # Main layout for the group box

        on_demand_form_layout = QFormLayout() # Form layout for label-input pairs
        on_demand_form_layout.setLabelAlignment(Qt.AlignLeft)
        on_demand_form_layout.setFormAlignment(Qt.AlignLeft)
        on_demand_form_layout.setHorizontalSpacing(10)
        on_demand_form_layout.setVerticalSpacing(5)

        # Add Original Filename display
        original_filename_label = QLabel(f"<b>Original Document:</b> {html.escape(self.original_filename)}")
        original_filename_label.setTextFormat(Qt.RichText)
        on_demand_main_layout.addWidget(original_filename_label)

        self.on_demand_name_label = QLabel("Full Name:")
        self.on_demand_name_input = QLineEdit()
        self.on_demand_name_input.setPlaceholderText("Full Name (required for AI parsing)")
        self.on_demand_name_input.setMaximumWidth(250)
        on_demand_form_layout.addRow(self.on_demand_name_label, self.on_demand_name_input)

        self.on_demand_email_label = QLabel("Email:")
        self.on_demand_email_input = QLineEdit()
        self.on_demand_email_input.setPlaceholderText("Email Address (optional hint for AI)")
        self.on_demand_email_input.setMaximumWidth(250)
        on_demand_form_layout.addRow(self.on_demand_email_label, self.on_demand_email_input)

        self.on_demand_phone_label = QLabel("Phone:")
        self.on_demand_phone_input = QLineEdit()
        self.on_demand_phone_input.setPlaceholderText("Phone Number (optional hint for AI)")
        self.on_demand_phone_input.setMaximumWidth(250)
        on_demand_form_layout.addRow(self.on_demand_phone_label, self.on_demand_phone_input)

        on_demand_main_layout.addLayout(on_demand_form_layout) # Add form layout to main layout

        self.trigger_parse_button = QPushButton("Parse and Populate Details with AI")
        self.trigger_parse_button.clicked.connect(self._trigger_on_demand_ai_parse)
        on_demand_main_layout.addWidget(self.trigger_parse_button, 0, Qt.AlignLeft) # Align button left

        self.on_demand_status_label = QLabel("")
        on_demand_main_layout.addWidget(self.on_demand_status_label)
        on_demand_main_layout.addStretch(1) # Add stretch to push content up

        self.on_demand_parse_group.setLayout(on_demand_main_layout)
        main_layout.addWidget(self.on_demand_parse_group)

        # Logic for automatic parsing based on settings
        auto_parse_setting = self.main_window_ref.settings.value("autoParseOnView", False, type=bool)
        name_is_unknown = self._is_field_unknown(self.resume_data_tuple[3]) # Check if name is unknown

        if auto_parse_setting and name_is_unknown:
            # If auto-parse is ON and the name is Unknown, trigger automatic parsing.
            # Hide manual input fields and the manual parse button.
            self.on_demand_name_label.setVisible(False)
            self.on_demand_name_input.setVisible(False)
            self.on_demand_email_label.setVisible(False)
            self.on_demand_email_input.setVisible(False)
            self.on_demand_phone_label.setVisible(False)
            self.on_demand_phone_input.setVisible(False)
            self.trigger_parse_button.setVisible(False)
            self.on_demand_parse_group.setVisible(True) # Show group for status label
            self._trigger_automatic_ai_parse()
        elif self.is_basic_resume:
            # If it's a "basic" resume (name, email, phone all unknown, etc.)
            # AND auto-parse is OFF (or name was somehow known but it's still basic),
            # then show the manual input mode.
            self.on_demand_parse_group.setVisible(True)
            # Ensure manual input fields are visible
            self.on_demand_name_label.setVisible(True)
            self.on_demand_name_input.setVisible(True)
            self.on_demand_email_label.setVisible(True)
            self.on_demand_email_input.setVisible(True)
            self.on_demand_phone_label.setVisible(True)
            self.on_demand_phone_input.setVisible(True)
            self.trigger_parse_button.setVisible(True)
        else:
            # Not a basic resume, and (auto-parse is off OR name is known and it's not otherwise basic)
            # So, hide the on-demand parsing group.
            self.on_demand_parse_group.setVisible(False)


        # --- Name (Editable) ---
        name_rank_score_layout = QHBoxLayout()

        self.name_display_label = QLabel()
        self.name_display_label.setTextFormat(Qt.RichText)
        name_rank_score_layout.addWidget(self.name_display_label, 1) # Give it more stretch

        self.name_edit_input = QLineEdit()
        self.name_edit_input.setVisible(False) # Hidden initially
        name_rank_score_layout.addWidget(self.name_edit_input, 1)

        # Rank and Score (Non-editable, part of header)
        rank_score_text_parts = []
        if self.rank not in ["N/A", ""]: rank_score_text_parts.append(f"<b>Rank:</b> {self.rank}")
        if self.score not in ["N/A", ""]: rank_score_text_parts.append(f"<b>Score:</b> {self.score}")

        if rank_score_text_parts:
            self.rank_score_label = QLabel(" | ".join(rank_score_text_parts))
            self.rank_score_label.setTextFormat(Qt.RichText)
            name_rank_score_layout.addWidget(self.rank_score_label)

        main_layout.addLayout(name_rank_score_layout)
        self._update_name_display() # Initialize name display

        # --- Edit/Save/Cancel Buttons ---
        self.edit_actions_layout = QHBoxLayout()
        self.edit_button = QPushButton("Edit Details")
        self.edit_button.setIcon(QIcon.fromTheme("document-edit"))
        self.edit_button.clicked.connect(self._enter_edit_mode)
        self.edit_actions_layout.addWidget(self.edit_button)

        self.save_button = QPushButton("Save Changes")
        self.save_button.setIcon(QIcon.fromTheme("document-save"))
        self.save_button.clicked.connect(self._save_changes)
        self.save_button.setVisible(False)
        self.edit_actions_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("Cancel Edit")
        self.cancel_button.setIcon(QIcon.fromTheme("edit-undo"))
        self.cancel_button.clicked.connect(lambda: self._toggle_edit_mode(False))
        self.cancel_button.setVisible(False)
        self.edit_actions_layout.addWidget(self.cancel_button)
        self.edit_actions_layout.addStretch()
        main_layout.addLayout(self.edit_actions_layout)

        # --- Main Scroll Area for Details ---
        self.details_scroll_area = QScrollArea()
        self.details_scroll_area.setWidgetResizable(True)
        self.details_scroll_content_widget = QWidget()
        self.details_scroll_layout = QVBoxLayout(self.details_scroll_content_widget)

        # --- Contact & File Information (with editable fields) ---
        contact_group = QGroupBox("Contact & File Information")
        contact_layout = QVBoxLayout()

        # Helper to create display label and edit input for a field
        def create_editable_field(label_text, initial_value_raw, is_multiline=False):
            field_layout = QHBoxLayout()
            # Only add the static label with colon if label_text is actually provided
            if label_text:
                static_label = QLabel(f"<b>{label_text}:</b>")
                static_label.setTextFormat(Qt.RichText)
                field_layout.addWidget(static_label)
            display_label = QLabel()
            display_label.setTextFormat(Qt.RichText) # For highlighting
            display_label.setWordWrap(True)

            if is_multiline: # Not used for current fields, but for future
                edit_input = QTextEdit()
                edit_input.setLineWrapMode(QTextEdit.WidgetWidth)
                edit_input.setFixedHeight(60) # Example height
            else:
                edit_input = QLineEdit()

            edit_input.setVisible(False)

            field_layout.addWidget(display_label, 1) # Display label takes more space
            field_layout.addWidget(edit_input, 1)    # Edit input takes more space

            # Set initial text (unhighlighted for edit, highlighted for display)
            edit_input.setText(str(initial_value_raw) if initial_value_raw else "")
            display_label.setText(self._highlight_text(str(initial_value_raw), self.search_terms) if initial_value_raw else "N/A")

            return field_layout, display_label, edit_input

        # Email
        email_layout, self.email_display_label, self.email_edit_input = \
            create_editable_field("Email", self.resume_data_tuple[4])
        self.contact_fields_layout = contact_layout # Store for repopulation
        self.contact_fields_layout.addLayout(email_layout)

        # Phone
        phone_layout, self.phone_display_label, self.phone_edit_input = \
            create_editable_field("Phone", self.resume_data_tuple[5])
        self.contact_fields_layout.addLayout(phone_layout)

        # Location
        location_layout, self.location_display_label, self.location_edit_input = \
            create_editable_field("Location", self.resume_data_tuple[6])
        self.contact_fields_layout.addLayout(location_layout)

        # Non-editable file info
        filename_val = html.escape(str(self.resume_data_tuple[1]))
        upload_date_val = html.escape(str(self.resume_data_tuple[2]))
        self.contact_fields_layout.addWidget(QLabel(f"<b>Original Filename:</b> {filename_val}"))
        self.contact_fields_layout.addWidget(QLabel(f"<b>Upload Date:</b> {upload_date_val}"))

        contact_group.setLayout(contact_layout)
        self.details_scroll_layout.addWidget(contact_group)

        # Unpack data (indices based on get_resume_details_by_id)
        # 0:id, 1:filename, 2:upload_date, 3:full_name, 4:email, 5:phone, 6:location,

        email_val = html.escape(str(self.resume_data_tuple[4]))
        # phone_val = html.escape(str(self.resume_data_tuple[5])) # Handled by create_editable_field
        # location_val = self._highlight_text(self.resume_data_tuple[6], self.search_terms) # Handled
        # filename_val = html.escape(str(self.resume_data_tuple[1])) # Handled
        # upload_date_val = html.escape(str(self.resume_data_tuple[2])) # Handled

        # --- Security Clearance (Editable) ---
        sec_clearance_group = QGroupBox("Security Clearance")
        sec_clearance_main_layout = QVBoxLayout()
        sec_clearance_layout, self.sec_clearance_display_label, self.sec_clearance_edit_input = \
            create_editable_field("", self._format_sec_clearance_for_display(self.resume_data_tuple[7])) # Label is part of group title
        sec_clearance_main_layout.addLayout(sec_clearance_layout)
        sec_clearance_group.setLayout(sec_clearance_main_layout)
        self.details_scroll_layout.addWidget(sec_clearance_group)

        # Populate the rest of the sections
        self._populate_dynamic_sections(self.details_scroll_layout)

        self.details_scroll_layout.addStretch(1)
        self.details_scroll_area.setWidget(self.details_scroll_content_widget)
        main_layout.addWidget(self.details_scroll_area)

        # Initial visibility based on whether it's a basic resume or auto-parsing
        if auto_parse_setting and name_is_unknown:
            self.details_scroll_area.setVisible(False) # Hide details until auto-parse completes
            self.edit_button.setVisible(False)
        else:
            self.details_scroll_area.setVisible(not self.is_basic_resume)
            self.edit_button.setVisible(not self.is_basic_resume) # Edit button only if not basic

        # Buttons
        self.bottom_button_layout = QHBoxLayout()
        self.preview_button = QPushButton("Preview Original Resume")
        self.preview_button.setObjectName("detailPreviewButton") # Assign object name
        self.preview_button.setIcon(QIcon.fromTheme("document-open"))
        self.preview_button.clicked.connect(self._preview_resume_file)

        self.close_button = QPushButton("Back to Search Results")
        self.close_button.setObjectName("detailCloseButton") # Assign object name
        self.close_button.setIcon(QIcon.fromTheme("go-previous"))
        self.close_button.clicked.connect(self.main_window_ref.show_search_results_view)

        # Add buttons with stretch for alignment
        self.bottom_button_layout.addWidget(self.close_button)   # "Back to Search Results" on the left
        self.bottom_button_layout.addStretch(1)                   # Add stretch to push preview_button to the right
        self.bottom_button_layout.addWidget(self.preview_button) # "Preview Original Resume" on the right
        main_layout.addLayout(self.bottom_button_layout)
    def _populate_dynamic_sections(self, layout_to_populate_in):
        """Populates sections like Attributes, Objectives, Skills, Experience etc."""
        # Clear existing dynamic widgets from the layout if any (e.g., during repopulation)
        # This is a bit tricky if we are adding QGroupBoxes directly.
        # A safer way for repopulation is to have placeholder widgets or clear and re-add.
        # For now, this method is primarily for initial setup. Repopulation will call this.

        attributes = self._try_load_json(self.resume_data_tuple[8], 'list')
        self._add_section_html(layout_to_populate_in, "Key Attributes", self._format_list_as_html_bullets(attributes))

        objectives = self.resume_data_tuple[9] if self.resume_data_tuple[9] else "N/A"
        highlighted_objectives = self._highlight_text(objectives, self.search_terms)
        self._add_section_html(layout_to_populate_in, "Career Objectives / Summary", f"<p>{highlighted_objectives}</p>")

        skills = self._try_load_json(self.resume_data_tuple[10], 'dict')
        skills_html = ""
        if skills and any(s_list for s_list in skills.values()): # Check if any skill list is non-empty
            skills_html += self._format_list_as_html_bullets(skills.get('technical', []), "Technical Skills")
            skills_html += self._format_list_as_html_bullets(skills.get('soft', []), "Soft Skills")
            skills_html += self._format_list_as_html_bullets(skills.get('domain_specific', []), "Domain-Specific Skills")
            skills_html += self._format_list_as_html_bullets(skills.get('certifications_as_skills', []), "Certifications (mentioned in skills)")
        else:
            skills_html = "<p>N/A</p>"
        self._add_section_html(layout_to_populate_in, "Skills", skills_html)

        for idx, title, field_parser in [
            (11, "Work Experience", self._format_experience_html),
            (12, "Education", self._format_education_html),
            (13, "Certifications", self._format_certifications_html),
            (15, "Projects", self._format_projects_html)
        ]:
            data = self._try_load_json(self.resume_data_tuple[idx], 'list')
            self._add_section_html(layout_to_populate_in, title, field_parser(data))

        languages_data = self._try_load_json(self.resume_data_tuple[14], 'dict')
        lang_html_prog = self._format_list_as_html_bullets(languages_data.get('programming', []), "Programming Languages")
        lang_html_human = self._format_list_as_html_bullets(languages_data.get('human', []), "Human Languages")
        self._add_section_html(layout_to_populate_in, "Languages", lang_html_prog + lang_html_human)

    def _format_sec_clearance_for_display(self, sec_clearance_json_str):
        """Formats security clearance for display, handling JSON or plain string."""
        loaded_data = self._try_load_json(sec_clearance_json_str, 'list') # This will always return a list

        # Filter out empty or whitespace-only strings from the list items before joining
        valid_items = [str(item).strip() for item in loaded_data if str(item).strip()]
        display_string = ", ".join(valid_items)

        return display_string if display_string else "N/A"
    def _update_name_display(self):
        # Assumes self.resume_data_tuple[3] is the name
        # Update self.current_full_name from the tuple first
        self.current_full_name = str(self.resume_data_tuple[3])
        current_name_plain = self.current_full_name

        # Highlight the plain name string.
        # _highlight_text will handle HTML-escaping the current_name_plain before adding its own spans.
        highlighted_name_content = self._highlight_text(current_name_plain, self.search_terms)

        # Construct the final HTML for the label, wrapping the highlighted content in <h2>
        name_html_for_label = f"<h2>{highlighted_name_content}</h2>"

        self.name_display_label.setText(name_html_for_label)
        self.name_edit_input.setText(current_name_plain) # Edit input always gets the raw, plain text

    def _toggle_edit_mode(self, edit_mode_active):
        self.is_edit_mode = edit_mode_active

        # Name
        self.name_display_label.setVisible(not edit_mode_active)
        self.name_edit_input.setVisible(edit_mode_active)
        self.current_full_name = str(self.resume_data_tuple[3]) # Refresh from tuple
        if edit_mode_active:
            self.name_edit_input.setText(self.current_full_name)
        else: # Update display if exiting edit mode (e.g. after save)
            self._update_name_display()

        # Email
        self.email_display_label.setVisible(not edit_mode_active)
        self.email_edit_input.setVisible(edit_mode_active)
        self.current_email = str(self.resume_data_tuple[4]) # Refresh
        if edit_mode_active:
            self.email_edit_input.setText(self.current_email)
        else:
            self.email_display_label.setText(self._highlight_text(self.current_email, self.search_terms))

        # Phone
        self.phone_display_label.setVisible(not edit_mode_active)
        self.phone_edit_input.setVisible(edit_mode_active)
        self.current_phone = str(self.resume_data_tuple[5]) # Refresh
        if edit_mode_active:
            self.phone_edit_input.setText(self.current_phone)
        else:
            self.phone_display_label.setText(self._highlight_text(self.current_phone, self.search_terms))

        # Location
        self.location_display_label.setVisible(not edit_mode_active)
        self.location_edit_input.setVisible(edit_mode_active)
        current_location = str(self.resume_data_tuple[6]) # Refresh
        if edit_mode_active:
            self.location_edit_input.setText(current_location)
        else:
            self.location_display_label.setText(self._highlight_text(current_location, self.search_terms))

        # Security Clearance
        self.sec_clearance_display_label.setVisible(not edit_mode_active)
        self.sec_clearance_edit_input.setVisible(edit_mode_active)
        # For sec_clearance, the raw value from tuple is JSON string or plain string.
        # For editing, we want a user-friendly comma-separated string.
        raw_sec_clearance_val = self._format_sec_clearance_for_display(self.resume_data_tuple[7])
        if raw_sec_clearance_val == "N/A": raw_sec_clearance_val = "" # Edit empty if N/A
        if edit_mode_active:
            self.sec_clearance_edit_input.setText(raw_sec_clearance_val)
        else:
            self.sec_clearance_display_label.setText(self._highlight_text(raw_sec_clearance_val, self.search_terms))

        # Buttons
        self.edit_button.setVisible(not edit_mode_active)
        self.save_button.setVisible(edit_mode_active)
        self.cancel_button.setVisible(edit_mode_active)

        # Disable/Enable bottom buttons
        self.preview_button.setEnabled(not edit_mode_active)
        self.close_button.setEnabled(not edit_mode_active)

    def _enter_edit_mode(self):
        if not self.is_basic_resume: # Only allow edit mode if not basic (i.e., after full parse)
            self._toggle_edit_mode(True)
        else:
            QMessageBox.information(self, "Parse Required", "Please parse the resume details first before editing.")

    def _save_changes(self):
        updated_data = {
            'full_name': self.name_edit_input.text(),
            'email': self.email_edit_input.text(),
            'phone': self.phone_edit_input.text(),
            'location': self.location_edit_input.text(),
            'security_clearance': self.sec_clearance_edit_input.text() # DB manager will process this string
        }

        success, message = self.main_window_ref.db_manager.update_resume_basic_details(self.resume_id, updated_data)

        if success:
            QMessageBox.information(self, "Success", message)
            # Update internal resume_data_tuple to reflect changes for display
            # This is a bit manual; ideally, re-fetch from DB or have DB method return updated tuple
            new_tuple_list = list(self.resume_data_tuple)
            new_tuple_list[3] = updated_data['full_name']
            new_tuple_list[4] = updated_data['email']
            new_tuple_list[5] = updated_data['phone']
            new_tuple_list[6] = updated_data['location']
            # For security clearance, we need to store it in the way the tuple expects (JSON string or plain)
            # The DB manager handles the conversion for saving, but here we need to mimic for the tuple.
            # For simplicity, let's just use the user's input string for now for the tuple,
            # display logic will re-parse it. Re-fetching from DB is done in _on_demand_parse_complete.
            new_tuple_list[7] = updated_data['security_clearance'] # This might not be ideal if it was JSON before
            self.resume_data_tuple = tuple(new_tuple_list)
            self._update_name_display() # This will also update self.current_full_name
            self.setWindowTitle(f"Resume Details: {self.resume_data_tuple[3]}") # Update window title
            self._toggle_edit_mode(False) # Exit edit mode, this will refresh display labels
            self.main_window_ref.refresh_search_results_after_edit() # Notify main window
        else:
            QMessageBox.critical(self, "Error", f"Failed to save changes: {message}")

    def _format_experience_html(self, experiences):
        if not experiences: return "<p>N/A</p>"
        html_output = ""
        for exp in experiences:
            title = self._highlight_text(exp.get('title', 'N/A'), self.search_terms)
            company = self._highlight_text(exp.get('company', 'N/A'), self.search_terms)
            dates = html.escape(str(exp.get('dates', 'N/A'))) # Dates usually not highlighted
            html_output += f"<p><b>{title} at {company}</b> ({dates})</p>"
            html_output += "<ul>"
            responsibilities = exp.get('responsibilities', 'N/A')
            if isinstance(responsibilities, list):
                for resp in responsibilities:
                    html_output += f"<li>{self._highlight_text(resp, self.search_terms)}</li>"
            else:
                html_output += f"<li>{self._highlight_text(responsibilities, self.search_terms)}</li>"
            html_output += "</ul><hr>"
        return html_output[:-4] if html_output else "<p>N/A</p>"

    def _format_education_html(self, educations):
        if not educations: return "<p>N/A</p>"
        html_output = ""
        for edu in educations:
            degree = self._highlight_text(edu.get('degree', 'N/A'), self.search_terms)
            institution = self._highlight_text(edu.get('institution', 'N/A'), self.search_terms)
            grad_date = html.escape(str(edu.get('graduation_date', 'N/A')))
            html_output += f"<p><b>{degree}</b> - {institution} ({grad_date})</p>"
            if edu.get('coursework'):
                coursework = self._highlight_text(edu.get('coursework'), self.search_terms)
                html_output += f"<p><i>Relevant Coursework/Thesis:</i> {coursework}</p>"
            html_output += "<hr>"
        return html_output[:-4] if html_output else "<p>N/A</p>"

    def _format_certifications_html(self, certifications):
        if not certifications: return "<p>N/A</p>"
        html_output = "<ul>"
        for cert in certifications:
            name = self._highlight_text(cert.get('name', 'N/A'), self.search_terms)
            issuer = self._highlight_text(cert.get('issuer', 'N/A'), self.search_terms)
            date = html.escape(str(cert.get('date', 'N/A')))
            html_output += f"<li><b>{name}</b> - Issued by: {issuer} (Date: {date})</li>"
        html_output += "</ul>"
        return html_output

    def _format_projects_html(self, projects):
        if not projects: return "<p>N/A</p>"
        html_output = ""
        for proj in projects:
            name = self._highlight_text(proj.get('name', 'N/A'), self.search_terms)
            description = self._highlight_text(proj.get('description', 'N/A'), self.search_terms)
            html_output += f"<p><b>{name}</b></p><p>{description}</p><hr>"
        return html_output[:-4] if html_output else "<p>N/A</p>"

    def _preview_resume_file(self):
        # This method now directly uses self.file_path
        if not self.file_path:
            QMessageBox.warning(self, "File Path Not Found", "The resume file path is not available.")
            return
        self.main_window_ref.preview_resume_from_path(self.file_path) # Call MainWindow's method

    def _clear_layout(self, layout):
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    sub_layout = item.layout()
                    if sub_layout is not None:
                        self._clear_layout(sub_layout)

    def _repopulate_all_fields(self):
        """Refreshes all display fields based on the current self.resume_data_tuple."""
        # Update name display (also updates self.current_full_name)
        self._update_name_display()
        self.setWindowTitle(f"Resume Details: {self.current_full_name}")

        # Update editable contact fields (display labels)
        self.email_display_label.setText(self._highlight_text(str(self.resume_data_tuple[4]), self.search_terms))
        self.phone_display_label.setText(self._highlight_text(str(self.resume_data_tuple[5]), self.search_terms))
        self.location_display_label.setText(self._highlight_text(str(self.resume_data_tuple[6]), self.search_terms))

        # Update security clearance display
        sec_clearance_display_val = self._format_sec_clearance_for_display(self.resume_data_tuple[7])
        self.sec_clearance_display_label.setText(self._highlight_text(sec_clearance_display_val, self.search_terms))
        while self.details_scroll_layout.count() > 2: # Assuming 0=Contact, 1=SecurityClearance
            item = self.details_scroll_layout.takeAt(2) # Remove items from index 2 onwards
            if item:
                widget = item.widget()
                if widget:
                    widget.deleteLater()

        # Repopulate dynamic sections
        self._populate_dynamic_sections(self.details_scroll_layout)
        self.details_scroll_layout.addStretch(1) # Re-add stretch

    def _trigger_on_demand_ai_parse(self):
        user_name = self.on_demand_name_input.text().strip()
        user_email = self.on_demand_email_input.text().strip()
        user_phone = self.on_demand_phone_input.text().strip()

        if not user_name: # Only Name is now required
            QMessageBox.warning(self, "Input Required", "Please provide the Full Name to proceed with AI parsing.")
            return
        # Email and Phone are now optional, so we don't validate their presence here.
        api_key = self.main_window_ref.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Missing", "API Key is not configured in Settings.")
            return

        self.trigger_parse_button.setEnabled(False)
        self.on_demand_status_label.setText("Parsing with AI... Please wait.")

        # raw_text_content and original_filename are already instance variables
        self.single_parser_thread = SingleResumeAIParser(
            self.resume_id, self.raw_text_content, self.original_filename,
            user_name, user_email, user_phone, api_key, self.main_window_ref.db_manager
        )
        self.single_parser_thread.complete_signal.connect(self._on_demand_parse_complete)
        self.single_parser_thread.error_signal.connect(self._on_demand_parse_error)
        self.single_parser_thread.start()

    def _trigger_automatic_ai_parse(self):
        """Triggers AI parsing automatically without manual input for name/email/phone."""
        api_key = self.main_window_ref.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Missing", "API Key is not configured in Settings. Cannot auto-parse.")
            self.on_demand_status_label.setText("Auto-parse failed: API Key missing.")
            return

        # No trigger_parse_button to disable here, as it's hidden in this mode.
        self.on_demand_status_label.setText("Automatically parsing with AI... Please wait.")

        # Pass "Unknown" or empty strings for user-provided details
        self.single_parser_thread = SingleResumeAIParser(
            self.resume_id, self.raw_text_content, self.original_filename,
            "Unknown", "", "", # Name, Email, Phone hints are unknown/empty
            api_key, self.main_window_ref.db_manager
        )
        self.single_parser_thread.complete_signal.connect(self._on_demand_parse_complete)
        self.single_parser_thread.error_signal.connect(self._on_demand_parse_error)
        self.single_parser_thread.start()
        self.main_window_ref.log_text.append(f"Resume ID {self.resume_id}: Triggered automatic AI parsing.")


    def _on_demand_parse_complete(self, resume_id, parsed_data):
        self.on_demand_status_label.setText("AI Parsing complete. Updating database...")
        success, message = self.main_window_ref.db_manager.update_full_resume_data(resume_id, parsed_data)

        if success:
            self.on_demand_status_label.setText("Database updated. Refreshing view...")
            self.resume_data_tuple = self.main_window_ref.db_manager.get_resume_details_by_id(resume_id)
            self.is_basic_resume = False # It's no longer basic

            self.on_demand_parse_group.setVisible(False)
            self.details_scroll_area.setVisible(True)
            self.edit_button.setVisible(True) # Enable editing now

            self._repopulate_all_fields() # Refresh the entire display
            self.main_window_ref.refresh_search_results_after_edit() # Update table in main window
            # QMessageBox.information(self, "Success", "Resume details parsed and updated successfully.") # Removed this pop-up
        else:
            QMessageBox.critical(self, "DB Update Error", f"Failed to save parsed details: {message}")
            self.on_demand_status_label.setText(f"Error saving to DB: {message}")

        # Re-enable button only if it was visible (i.e., not in auto-parse mode)
        if self.trigger_parse_button.isVisible():
            self.trigger_parse_button.setEnabled(True)

    def _on_demand_parse_error(self, error_msg):
        QMessageBox.critical(self, "AI Parsing Error", f"Error during AI parsing: {error_msg}")
        self.on_demand_status_label.setText(f"AI Parsing Error: {error_msg}")
        # Re-enable button only if it was visible
        if self.trigger_parse_button.isVisible():
            self.trigger_parse_button.setEnabled(True)
class MainWindow(QMainWindow):
    """Main application window"""
    def __init__(self):
        super().__init__()

        self.last_search_query = "" # To store the last used search query
        self.last_boolean_search_query = "" # To store the last used boolean search query
        self.last_boolean_search_terms = [] # Store parsed terms for highlighting
        self.boolean_keyword_widgets = [] # For KeywordStatusWidget instances

        self.last_query_analysis = None # To store the result from AIQueryAnalyzer

        self.last_ai_search_keywords = [] # Store AI-extracted keywords for highlighting
        self.setWindowTitle(APP_TITLE)
        self.setMinimumSize(1000, 700)

        # Initialize search worker thread and related UI components
        self.search_worker = None
        self.search_progress_bar = None
        self.search_cancel_button = None

        # Initialize QSettings
        # Use specific names for your organization and application
        self.settings = QSettings("ChipSoft", "ResumeQuickFinder")
        # Ensure _temp_resume_parser_for_utils is initialized early
        # It's needed for text extraction in preview methods.
        # For previews, DOCX to PDF conversion is likely not desired by default, so pass False.
        # Determine DB path to use for DatabaseManager initialization
        self.current_db_path = self.settings.value(DB_PATH_SETTING_KEY, DB_PATH)

        self._temp_resume_parser_for_utils = ResumeParser([], "", DatabaseManager(DB_PATH), auto_convert_docx_to_pdf=False)
        self.current_preview_highlight_terms = []
        self._terms_for_current_web_load = [] # For QWebEngineView callback

        # Initialize database manager
        self.db_manager = DatabaseManager(DB_PATH)

        # Set up UI (this will call setup_settings_tab, which might need self.current_db_path)
        self.setup_ui()

        # Apply dark theme
        self.setStyleSheet(DARK_THEME)

        # Load settings after UI is set up
        self.load_app_settings()

        # Maximize the window on startup
        self.showMaximized()

        # Initialize the Search & Results tab data on startup
        self._initialize_search_results_on_startup()

    def _reconstruct_path_for_preview(self, stored_path: str) -> str:
        """
        Reconstructs an absolute path from a stored path that might have had a prefix stripped.
        """
        if not stored_path:
            return ""
        norm_stored_path = os.path.normpath(stored_path)

        # Get custom document base path from settings
        custom_doc_base_path = self.settings.value(DOC_BASE_PATH_SETTING_KEY, "").strip()

        # --- Attempt 1: Use Custom Document Base Path ---
        if custom_doc_base_path and os.path.isdir(custom_doc_base_path):
            path_suffix_to_join = ""
            try:
                common_path_lower = COMMON_ONEDRIVE_RELATIVE_PATH.lower()
                norm_stored_path_lower = norm_stored_path.lower()

                idx = norm_stored_path_lower.rfind(common_path_lower)

                if idx != -1:
                    suffix_start_index = idx + len(COMMON_ONEDRIVE_RELATIVE_PATH)
                    path_suffix_to_join = norm_stored_path[suffix_start_index:].lstrip(os.sep)
                else:
                    if norm_stored_path.startswith(os.sep) and not os.path.splitdrive(norm_stored_path)[0]:
                        path_suffix_to_join = norm_stored_path.lstrip(os.sep)
                    else:
                        path_suffix_to_join = os.path.basename(norm_stored_path)
            except Exception: # Should be rare
                path_suffix_to_join = os.path.basename(norm_stored_path)

            if path_suffix_to_join:
                candidate_path = os.path.join(custom_doc_base_path, path_suffix_to_join)
                if os.path.exists(candidate_path):
                    self.log_text.append(f"Preview path found using custom base: {candidate_path}")
                    return candidate_path

        # --- Attempt 2: Exact Stored Absolute Path ---
        if os.path.isabs(norm_stored_path) and os.path.exists(norm_stored_path):
            self.log_text.append(f"Preview path found using direct absolute stored path: {norm_stored_path}")
            return norm_stored_path

        # --- Attempt 3: Transformed Path (relative to current user's home) ---
        if norm_stored_path.startswith(os.sep) and not os.path.splitdrive(norm_stored_path)[0]:
            reconstructed_user_home = os.path.join(USER_HOME_DIR, norm_stored_path.lstrip(os.sep))
            if os.path.exists(reconstructed_user_home):
                self.log_text.append(f"Preview path found using USER_HOME_DIR reconstruction: {reconstructed_user_home}")
                return reconstructed_user_home

        # --- Attempt 4: Absolute Path, Reconstruct with Current User's Home + Common OneDrive Segment ---
        if os.path.isabs(norm_stored_path):
            try:
                common_segment_start_index = norm_stored_path.lower().index(COMMON_ONEDRIVE_RELATIVE_PATH.lower())
                shared_part = norm_stored_path[common_segment_start_index:]

                reconstructed_user_home_plus_common = os.path.join(USER_HOME_DIR, shared_part)
                if os.path.exists(reconstructed_user_home_plus_common):
                    self.log_text.append(f"Preview path found by reconstructing absolute with USER_HOME_DIR: {reconstructed_user_home_plus_common}")
                    return reconstructed_user_home_plus_common

                # --- Attempt 5: Fallback to DAVID_FALLBACK_ONEDRIVE_BASE ---
                if shared_part.lower().startswith(COMMON_ONEDRIVE_RELATIVE_PATH.lower()):
                    suffix_for_david_base = shared_part[len(COMMON_ONEDRIVE_RELATIVE_PATH):].lstrip(os.sep)
                    david_fallback_path = os.path.join(DAVID_FALLBACK_ONEDRIVE_BASE, suffix_for_david_base)
                    if os.path.exists(david_fallback_path):
                        self.log_text.append(f"Preview path found using DAVID_FALLBACK_ONEDRIVE_BASE: {david_fallback_path}")
                        return david_fallback_path
            except ValueError:
                pass # COMMON_ONEDRIVE_RELATIVE_PATH not found.

        # --- Final Fallback ---
        self.log_text.append(f"Preview path not found through reconstruction attempts. Returning original: {norm_stored_path}")
        return norm_stored_path

    def setup_ui(self):
        """Set up the user interface"""
        # Main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # Create tabs
        self.tabs_widget = QTabWidget() # Store as instance variable
        upload_tab = QWidget()
        self.search_results_tab_widget = QWidget() # Store as instance variable
        settings_tab = QWidget()

        # Desired order: 1. Search & Results, 2. Upload & Process, 3. Settings
        self.tabs_widget.addTab(self.search_results_tab_widget, "Search & Results")
        self.tabs_widget.addTab(upload_tab, "Upload & Process")
        self.tabs_widget.addTab(settings_tab, "Settings")

        # Set up upload tab
        self.setup_upload_tab(upload_tab)
        # Set up search tab (now the first tab, but setup can be in any order before addTab)
        self.setup_search_tab(self.search_results_tab_widget)

        # Set up settings tab
        self.setup_settings_tab(settings_tab)

        # Connect tab changed signal
        self.tabs_widget.currentChanged.connect(self.on_tab_switched)

        main_layout.addWidget(self.tabs_widget)
        self.setCentralWidget(main_widget)

    def setup_settings_tab(self, tab):
        """Set up the settings tab"""
        layout = QVBoxLayout(tab)

        # API Key Configuration section
        api_key_group = QGroupBox(" API Key Configuration")
        api_key_layout_main = QVBoxLayout() # Main layout for this group

        api_key_input_layout = QHBoxLayout() # Layout for input and save button
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setPlaceholderText("Enter your  API Key")
        api_key_input_layout.addWidget(self.api_key_input, 1) # Add with stretch factor

        save_api_key_button = QPushButton("Save API Key")
        save_api_key_button.clicked.connect(self.save_api_key_setting)
        api_key_input_layout.addWidget(save_api_key_button)

        api_key_layout_main.addLayout(api_key_input_layout)
        api_key_layout_main.addWidget(QLabel("Changes to the API key require a restart or re-saving to take full effect if currently in use."))
        api_key_group.setLayout(api_key_layout_main)
        layout.addWidget(api_key_group)

        # Database Path Configuration section
        db_config_group = QGroupBox("Database File Location")
        db_config_layout = QVBoxLayout()

        db_path_input_layout = QHBoxLayout()
        self.db_path_input = QLineEdit()
        self.db_path_input.setPlaceholderText(f"Current/Default: {DB_PATH}")
        self.db_path_input.setReadOnly(True) # Path is set via Browse
        db_path_input_layout.addWidget(self.db_path_input, 1)

        browse_db_button = QPushButton("Browse...")
        browse_db_button.clicked.connect(self.browse_db_file)
        db_path_input_layout.addWidget(browse_db_button)
        db_config_layout.addLayout(db_path_input_layout)

        save_db_path_button = QPushButton("Save Path & Restart App")
        save_db_path_button.clicked.connect(self.save_db_path_and_prompt_restart)
        db_config_layout.addWidget(save_db_path_button, 0, Qt.AlignLeft)

        db_config_layout.addWidget(QLabel("Changes to the database path require an application restart to take effect."))
        db_config_group.setLayout(db_config_layout)
        layout.addWidget(db_config_group)

        # Resume Document Base Path Configuration section
        doc_base_path_group = QGroupBox("Resume Document Base Path (for Previews)")
        doc_base_path_layout = QVBoxLayout()

        doc_base_path_input_layout = QHBoxLayout()
        self.doc_base_path_input = QLineEdit()
        self.doc_base_path_input.setPlaceholderText("Enter path to folder containing resume documents")
        doc_base_path_input_layout.addWidget(self.doc_base_path_input, 1)

        browse_doc_folder_button = QPushButton("Browse Folder...")
        browse_doc_folder_button.clicked.connect(self.browse_resume_doc_folder)
        doc_base_path_input_layout.addWidget(browse_doc_folder_button)
        doc_base_path_layout.addLayout(doc_base_path_input_layout)

        save_doc_base_path_button = QPushButton("Save Document Base Path")
        save_doc_base_path_button.clicked.connect(self.save_resume_doc_base_path_setting)
        doc_base_path_layout.addWidget(save_doc_base_path_button, 0, Qt.AlignLeft)

        doc_base_path_group.setLayout(doc_base_path_layout)
        layout.addWidget(doc_base_path_group)

        # General Settings Group (renamed from Search Configuration)
        search_settings_group = QGroupBox("Search Configuration")
        search_settings_layout = QVBoxLayout()

        # Min AI Score elements are removed as ranking is removed.

        self.auto_parse_checkbox = QCheckBox("Automatically parse basic resumes on view")
        self.auto_parse_checkbox.setToolTip("If checked, when viewing a resume with 'Unknown' details, AI parsing will start automatically without manual input.")
        self.auto_parse_checkbox.setChecked(self.settings.value("autoParseOnView", False, type=bool))
        self.auto_parse_checkbox.stateChanged.connect(self.save_auto_parse_setting)

        self.basic_upload_checkbox = QCheckBox("Basic Upload (skip AI parsing, store raw text only)")
        self.basic_upload_checkbox.setToolTip("If checked, resumes are added with minimal data; AI parsing can be done later from the details view. This is the default behavior.")
        self.basic_upload_checkbox.setChecked(self.settings.value("basicUploadEnabled", True, type=bool)) # Default to True
        self.basic_upload_checkbox.stateChanged.connect(self.save_basic_upload_setting)

        self.auto_convert_docx_checkbox = QCheckBox("Automatically convert DOCX to PDF for text extraction")
        self.auto_convert_docx_checkbox.setToolTip("If checked, .docx files will be converted to .pdf before text extraction (may improve accuracy but can be slower). If unchecked, text is extracted directly from .docx (faster). Default is off.")
        self.auto_convert_docx_checkbox.setChecked(self.settings.value("autoConvertDocxToPdf", False, type=bool)) # Default to False (off)
        self.auto_convert_docx_checkbox.stateChanged.connect(self.save_auto_convert_docx_setting)

        self.auto_parse_checkbox.stateChanged.connect(self.save_auto_parse_setting)
        search_settings_layout.addWidget(self.auto_parse_checkbox)

        compact_button_style = "QPushButton { padding: 5px 10px; }" # Reduced padding
        # If you want to set a max width, you could add: "max-width: 150px;"
        # e.g., compact_button_style = "QPushButton { padding: 5px 10px; max-width: 180px; }"

        search_settings_layout.addWidget(self.basic_upload_checkbox) # Add the basic_upload_checkbox here
        search_settings_layout.addWidget(self.auto_convert_docx_checkbox) # Add the new checkbox
        layout.addWidget(search_settings_group)

        db_management_group = QGroupBox("Database Management")
        advanced_utilities_group = QGroupBox("Advanced Utilities") # New combined group
        advanced_utilities_layout = QVBoxLayout()

        buttons_layout = QHBoxLayout() # For side-by-side buttons

        # Database optimization buttons
        optimize_db_button = QPushButton("Optimize Database")
        optimize_db_button.setToolTip("Rebuild indexes and optimize database for better performance with large datasets.")
        optimize_db_button.clicked.connect(self.optimize_database_performance)
        optimize_db_button.setStyleSheet(compact_button_style)
        buttons_layout.addWidget(optimize_db_button)

        db_stats_button = QPushButton("Database Statistics")
        db_stats_button.setToolTip("View database performance statistics and information.")
        db_stats_button.clicked.connect(self.show_database_statistics)
        db_stats_button.setStyleSheet(compact_button_style)
        buttons_layout.addWidget(db_stats_button)

        clear_db_button = QPushButton("Clear Entire Database")
        clear_db_button.setToolTip("Deletes all resumes and associated data from the database. This action cannot be undone.")
        clear_db_button.clicked.connect(self.confirm_clear_database)
        clear_db_button.setStyleSheet(compact_button_style) # Apply compact style
        buttons_layout.addWidget(clear_db_button)

        self.fix_data_button = QPushButton("AI Fix Suspicious Phone Numbers & Locations")
        self.fix_data_button.setToolTip("Identifies resumes with potentially unformatted or incomplete phone numbers/locations and uses AI to attempt correction. Targets specific entries to save time and API credits.")
        self.fix_data_button.clicked.connect(self.start_data_correction_process)
        self.fix_data_button.setStyleSheet(compact_button_style) # Apply compact style
        buttons_layout.addWidget(self.fix_data_button)

        buttons_layout.addStretch() # Pushes buttons to the left
        advanced_utilities_layout.addLayout(buttons_layout)

        advanced_utilities_group.setLayout(advanced_utilities_layout)
        layout.addWidget(advanced_utilities_group)

        layout.addStretch() # Pushes the group box to the top

        # Add a reference to the log_text from the upload tab for logging setting changes
        # This assumes self.log_text is initialized in setup_upload_tab
        # If not, you might need a shared logging mechanism or pass it around.
        # For simplicity, we'll assume it's available.
        # If self.log_text is not yet created when this is called, this will error.
        # Ensure setup_upload_tab is called before setup_settings_tab if direct access is needed.

    def setup_upload_tab(self, tab):
        """Set up the upload and processing tab"""
        layout = QVBoxLayout(tab)

        # API Key input is now in the Settings tab

        # Upload section
        upload_group = QGroupBox("Upload Resumes")
        upload_layout = QVBoxLayout()

        # File selection
        file_selection_layout = QHBoxLayout() # Renamed for clarity
        select_button = QPushButton("Select Files")
        select_button.clicked.connect(self.select_files)
        file_selection_layout.addWidget(select_button)

        select_folder_button = QPushButton("Select Folder")
        select_folder_button.clicked.connect(self.select_folder)
        file_selection_layout.addWidget(select_folder_button)
        file_selection_layout.addStretch(1) # Add stretch to keep buttons to the left
        upload_layout.addLayout(file_selection_layout)

        # Progress bar
        progress_bar_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_bar_layout.addWidget(self.progress_bar, 3)  # Stretch factor 3 (approx 30%)
        progress_bar_layout.addStretch(7)                   # Stretch factor 7 (approx 70%) for spacing
        upload_layout.addLayout(progress_bar_layout)

        # Process button
        process_button_layout = QHBoxLayout()
        self.process_button = QPushButton("Process Resumes")
        self.process_button.clicked.connect(self.process_resumes)
        self.process_button.setEnabled(False)
        process_button_layout.addWidget(self.process_button, 3) # Stretch factor 3 (approx 30%)
        process_button_layout.addStretch(7)                    # Stretch factor 7 (approx 70%) for spacing
        upload_layout.addLayout(process_button_layout)

        # Status label
        self.status_label = QLabel("Ready")
        upload_layout.addWidget(self.status_label)

        upload_group.setLayout(upload_layout)
        layout.addWidget(upload_group)

        # Log section
        log_group = QGroupBox("Processing Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

    def setup_search_tab(self, tab):
        """Set up the search and results tab"""
        # Main layout for the tab will hold the QStackedWidget
        tab_layout = QVBoxLayout(tab)

        # Create the QStackedWidget
        self.search_tab_stack = QStackedWidget()
        tab_layout.addWidget(self.search_tab_stack)

        # --- Page 0: Main Search, Results, and Preview View ---
        self.search_results_page_widget = QWidget()
        # Use QHBoxLayout for the main page to accommodate the splitter
        main_search_page_layout = QHBoxLayout(self.search_results_page_widget)
        self.search_tab_stack.addWidget(self.search_results_page_widget)

        # Create a QSplitter for table and preview
        self.splitter = QSplitter(Qt.Horizontal)
        main_search_page_layout.addWidget(self.splitter)

        # --- Left Pane of Splitter: Search and Results Table ---
        left_pane_widget = QWidget()
        search_and_results_layout = QVBoxLayout(left_pane_widget) # This will hold search group, results group etc.
        self.splitter.addWidget(left_pane_widget)

        # --- Right Pane of Splitter: Resume Preview ---
        right_pane_widget = QWidget()
        preview_area_layout = QVBoxLayout(right_pane_widget)

        self.preview_stacked_widget = QStackedWidget()
        preview_area_layout.addWidget(self.preview_stacked_widget)

        self.preview_web_view = QWebEngineView()
        # Explicitly enable PDF viewer and plugins
        web_settings = self.preview_web_view.settings()
        web_settings.setAttribute(QWebEngineSettings.PluginsEnabled, True)
        web_settings.setAttribute(QWebEngineSettings.PdfViewerEnabled, True)
        web_settings.setAttribute(QWebEngineSettings.AutoLoadImages, True) # Ensure images are loaded
        web_settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True) # May help with some HTML/CSS rendering
        self.preview_web_view.loadFinished.connect(self._on_preview_load_finished) # Connect loadFinished signal
        self.preview_stacked_widget.addWidget(self.preview_web_view) # Page 0

        self.preview_text_edit = QTextEdit()
        self.preview_text_edit.setReadOnly(True)
        self.preview_stacked_widget.addWidget(self.preview_text_edit) # Page 1

        self.preview_placeholder_label = QLabel("Select a resume from the table to preview.")
        self.preview_placeholder_label.setAlignment(Qt.AlignCenter)
        self.preview_stacked_widget.addWidget(self.preview_placeholder_label) # Page 2
        self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
        self.splitter.addWidget(right_pane_widget)

        # Search section
        search_group = QGroupBox("Search Resumes")
        search_layout = QVBoxLayout(search_group) # search_group is the parent
        search_and_results_layout.addWidget(search_group, 0) # Add to left pane, stretch factor 0

        # Search input
        input_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter job requirements or skills (e.g., 'Python developer with 5 years experience')")
        self.search_input.textChanged.connect(self.handle_search_input_changed) # Connect signal
        # search_button needs to be defined before connecting its click signal to returnPressed
        search_button = QPushButton("Search")
        search_button.clicked.connect(self.search_resumes)
        self.search_input.returnPressed.connect(search_button.click) # Trigger search on Enter
        # AI Ranking checkbox removed

        input_layout.addWidget(self.search_input)
        input_layout.addWidget(search_button)
        search_layout.addLayout(input_layout)
        # Add a separator and new boolean search section
        line_separator = QFrame()
        line_separator.setObjectName("line") # For stylesheet styling
        line_separator.setFrameShape(QFrame.HLine)
        line_separator.setFrameShadow(QFrame.Sunken)
        search_layout.addWidget(line_separator)
        search_layout.addSpacing(10) # Add some vertical space before the new section

        boolean_search_label = QLabel("<b>Resume Keyword Search (e.g., skill1 AND \"exact phrase\" OR skill2):</b>")
        boolean_search_label.setTextFormat(Qt.RichText)
        search_layout.addWidget(boolean_search_label)

        boolean_input_layout = QHBoxLayout()
        self.boolean_search_input = QLineEdit()
        self.boolean_search_input.setPlaceholderText("Enter boolean keyword query (AND/OR supported)...")
        self.boolean_search_input.textChanged.connect(self.handle_boolean_search_input_changed) # Connect signal

        self.search_raw_text_only_checkbox = QCheckBox("Search in resume text body only")
        self.search_raw_text_only_checkbox.setToolTip("If checked, keyword search will only look within the main extracted text of the resume, not in tags, location, etc.")

        self.boolean_search_button = QPushButton("Keyword Search")
        self.boolean_search_button.clicked.connect(self.perform_boolean_search)
        self.boolean_search_input.returnPressed.connect(self.boolean_search_button.click) # Trigger search on Enter

        boolean_input_layout.addWidget(self.boolean_search_input, 1) # Assign stretch factor 1
        boolean_input_layout.addWidget(self.search_raw_text_only_checkbox) # Add checkbox
        boolean_input_layout.addWidget(self.boolean_search_button)

        search_layout.addLayout(boolean_input_layout)
        boolean_input_layout.addStretch(1) # Add a stretch factor to consume extra space

        # Progress bar and cancel button for search operations
        search_progress_layout = QHBoxLayout()
        self.search_progress_bar = QProgressBar()
        self.search_progress_bar.setVisible(False)
        self.search_progress_bar.setMinimum(0)
        self.search_progress_bar.setMaximum(100)
        self.search_progress_bar.setTextVisible(True)

        self.search_cancel_button = QPushButton("Cancel Search")
        self.search_cancel_button.setVisible(False)
        self.search_cancel_button.clicked.connect(self.cancel_search)

        search_progress_layout.addWidget(self.search_progress_bar, 3)  # Stretch factor 3
        search_progress_layout.addWidget(self.search_cancel_button, 1)  # Stretch factor 1
        search_layout.addLayout(search_progress_layout)

        # Area for displaying boolean keyword statuses
        self.keyword_status_scroll_area = QScrollArea()
        self.keyword_status_scroll_area.setWidgetResizable(True)
        self.keyword_status_scroll_area.setFixedHeight(45) # Adjust as needed
        self.keyword_status_widget_container = QWidget()
        self.boolean_keyword_status_layout = QHBoxLayout(self.keyword_status_widget_container)
        self.boolean_keyword_status_layout.setAlignment(Qt.AlignLeft)
        self.boolean_keyword_status_layout.setSpacing(10) # Spacing between keyword widgets
        self.keyword_status_scroll_area.setWidget(self.keyword_status_widget_container)
        search_layout.addWidget(self.keyword_status_scroll_area)
        self.keyword_status_scroll_area.setVisible(False) # Initially hidden
        search_layout.addStretch(1) # Add stretch to consume extra vertical space in the search_group


        # Min AI Score elements have been moved to the Settings tab

        # Results section
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group) # results_group is the parent
        search_and_results_layout.addWidget(results_group, 1) # Add to left pane, stretch factor 1

        # Status label for search
        self.search_status_label = QLabel("Ready to search")
        results_layout.addWidget(self.search_status_label)

        # Table for results
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(9)
        self.results_table.setHorizontalHeaderLabels([
            "Rank", "Score", "Name", "Email", "Phone", "Location", "Security Clearance", "Attributes", "File"
        ])
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.itemDoubleClicked.connect(self.handle_table_item_double_clicked) # Updated connection
        self.results_table.setSortingEnabled(False) # Disable sorting

        # Configure column resize modes for better consistency
        header = self.results_table.horizontalHeader()
        # 0: Rank, 1: Score, 2: Name, 3: Email, 4: Phone, 5: Location, 6: Sec Clearance, 7: Attributes, 8: File
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents) # Rank
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents) # Score
        header.setSectionResizeMode(2, QHeaderView.Stretch)          # Name
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents) # Email
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents) # Phone
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents) # Location
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents) # Security Clearance
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents) # Attributes (hidden, but set mode)
        header.setSectionResizeMode(8, QHeaderView.Stretch)          # File

        # Hide the "Attributes" column (index 7)
        self.results_table.setColumnHidden(7, True)

        # Connect itemSelectionChanged to handle delete button visibility
        self.results_table.itemSelectionChanged.connect(self.handle_table_selection_changed)

        results_layout.addWidget(self.results_table)

        # QHBoxLayout for action buttons below the table
        action_buttons_layout = QHBoxLayout()

        self.delete_selected_button = QPushButton("Delete Selected Resumes")
        self.delete_selected_button.setObjectName("deleteResumesButton") # For styling
        self.delete_selected_button.setIcon(QIcon.fromTheme("edit-delete"))
        self.delete_selected_button.clicked.connect(self.confirm_delete_selected_resumes)
        self.delete_selected_button.setEnabled(False) # Initially disabled
        action_buttons_layout.addWidget(self.delete_selected_button)

        # Create and add the Bulk Analyse button
        self.bulk_analyse_button = QPushButton("Bulk AI Analyse Selected")
        self.bulk_analyse_button.setIcon(QIcon.fromTheme("system-run")) # Example icon
        self.bulk_analyse_button.setToolTip("Performs AI analysis on selected resumes that have 'Unknown' details.")
        self.bulk_analyse_button.clicked.connect(self.bulk_ai_analyse_selected_resumes)
        self.bulk_analyse_button.setEnabled(False) # Initially disabled
        action_buttons_layout.addWidget(self.bulk_analyse_button)

        # AI Rank Selected button removed
        # Extend Search button removed

        action_buttons_layout.addStretch()
        search_and_results_layout.addLayout(action_buttons_layout) # Add to left pane

        # Old way of adding delete button, now replaced by action_buttons_layout

        # Query analysis section (initially hidden)
        self.query_analysis_group = QGroupBox("Search Query Analysis (AI Generated)")
        query_analysis_main_layout = QVBoxLayout(self.query_analysis_group)
        search_and_results_layout.addWidget(self.query_analysis_group, 0) # Add to left pane, stretch factor 0
        self.query_analysis_group.setVisible(False)
        query_analysis_layout = QVBoxLayout()

        # Create a scroll area for the analysis content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(600)
        scroll_content = QWidget()
        self.query_analysis_content_layout = QVBoxLayout(scroll_content) # Renamed for clarity
        self.query_analysis_content_layout.setContentsMargins(5, 5, 5, 5) # Small margins
        self.query_analysis_content_layout.setSpacing(2) # Reduced spacing

        # Add a label for each category
        self.analysis_labels = {}
        for category in ["technical_skills", "experience", "education", "soft_skills",
                         "industry_knowledge", "security_clearance", "location", "other_requirements"]:
            # category_label = QLabel(f"<b>{category.replace('_', ' ').title()}:</b>") # Old: separate label for title
            # category_label.setTextFormat(Qt.RichText)
            # self.query_analysis_content_layout.addWidget(category_label)

            requirements_label = QLabel() # Will hold "Category: [Importance] requirements..."
            requirements_label.setWordWrap(True)
            requirements_label.setTextFormat(Qt.RichText)
            # requirements_label.setIndent(0) # No indent or smaller indent
            self.query_analysis_content_layout.addWidget(requirements_label)
            self.analysis_labels[category] = requirements_label

            # Add some spacing
            self.query_analysis_content_layout.addSpacing(5)

        # Add search keywords section
        keywords_label = QLabel("<b>AI Search Keywords (used for DB filtering):</b>")
        keywords_label.setTextFormat(Qt.RichText)
        self.query_analysis_content_layout.addWidget(keywords_label)

        self.keywords_label = QLabel("None")
        self.keywords_label.setWordWrap(True)
        self.keywords_label.setTextFormat(Qt.RichText)
        # self.keywords_label.setIndent(20) # Removed indent
        self.query_analysis_content_layout.addWidget(self.keywords_label)

        # Finalize scroll area
        scroll_area.setWidget(scroll_content)
        query_analysis_main_layout.addWidget(scroll_area) # Add scroll_area to the group's layout

        # Set initial splitter sizes (e.g., 60% for table, 40% for preview)
        total_width = self.width() # Or a sensible default
        self.splitter.setSizes([int(total_width * 0.6), int(total_width * 0.4)])

    def select_files(self):
        """Open file dialog to select resume files"""
        file_filter = f"Resume Files (*.{' *.'.join(SUPPORTED_FORMATS)})"
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Select Resume Files", "", file_filter
        )

        if file_paths:
            self.selected_files = file_paths
            self.status_label.setText(f"{len(file_paths)} files selected. Ready to process.")
            self.process_button.setEnabled(True)
            self.log_text.append(f"Selected {len(file_paths)} files for processing.")
        else:
            self.selected_files = []
            self.status_label.setText("No files selected. Click 'Select Files' to begin.")
            self.process_button.setEnabled(False)

    def select_folder(self):
        """Open folder dialog to select a directory containing resume files."""
        folder_path = QFileDialog.getExistingDirectory(self, "Select Folder")

        if folder_path:
            self.log_text.append(f"Selected folder: {folder_path}. Scanning for supported files...")
            found_files = []
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().split('.')[-1] in SUPPORTED_FORMATS:
                        full_path = os.path.join(root, file)
                        found_files.append(full_path)

            if found_files:
                self.selected_files = found_files
                self.status_label.setText(f"{len(found_files)} files found in folder. Ready to process.")
                self.process_button.setEnabled(True)
                self.log_text.append(f"Found {len(found_files)} supported files in {folder_path} and its subdirectories.")
                for f_path in found_files[:10]: # Log first 10 found files for brevity
                    self.log_text.append(f"  - {os.path.basename(f_path)}")
                if len(found_files) > 10:
                    self.log_text.append(f"  ...and {len(found_files) - 10} more.")
            else:
                self.selected_files = []
                self.status_label.setText(f"No supported resume files found in {folder_path}.")
                self.process_button.setEnabled(False)
                self.log_text.append(f"No supported files found in {folder_path}.")
        else:
            self.log_text.append("Folder selection cancelled.")

    def process_resumes(self):
        """Process the selected resume files"""
        api_key = self.api_key_input.text().strip()

        if not api_key:
            QMessageBox.warning(self, "API Key Required", "Please enter and save your  API Key in the Settings tab.")
            return

        if not hasattr(self, 'selected_files') or not self.selected_files:
            QMessageBox.warning(self, "No Files Selected", "Please select resume files to process.")
            return

        # Reset progress
        self.progress_bar.setValue(0)
        self.status_label.setText("Processing...")
        self.process_button.setEnabled(False)

        if self.basic_upload_checkbox.isChecked():
            auto_convert_docx = self.settings.value("autoConvertDocxToPdf", False, type=bool)
            self.log_text.append(f"Started basic processing for {len(self.selected_files)} resume files (AI parsing skipped).")
            self.basic_processor_thread = BasicResumeProcessor(self.selected_files, self.db_manager, auto_convert_docx)
            # Connect BasicResumeProcessor's error_signal to ResumeParser's error_signal handler for now
            # as ResumeParser's extract_text_from_document uses its own error_signal.
            # This is a bit of a hack due to method reuse. A cleaner way would be a shared error signal or utility.
            # For now, we'll make BasicResumeProcessor use its own error_signal for its specific errors,
            # and text extraction errors will be logged by ResumeParser's mechanism if we directly call its methods.
            # Let's adjust BasicResumeProcessor to use its own error_signal for text extraction.
            self.basic_processor_thread.progress_signal.connect(self.update_progress)
            self.basic_processor_thread.complete_signal.connect(self.basic_processing_complete) # New handler
            self.basic_processor_thread.error_signal.connect(self.parsing_error) # Can reuse parsing_error for logging
            self.basic_processor_thread.start()
        else:
            auto_convert_docx = self.settings.value("autoConvertDocxToPdf", False, type=bool)
            self.log_text.append(f"Started AI processing for {len(self.selected_files)} resume files...")
            self.parser_thread = ResumeParser(self.selected_files, api_key, self.db_manager, auto_convert_docx)
            self.parser_thread.progress_signal.connect(self.update_progress)
            self.parser_thread.complete_signal.connect(self.parsing_complete)
            self.parser_thread.error_signal.connect(self.parsing_error)
            self.parser_thread.start()

    def update_progress(self, value):
        """Update progress bar"""
        self.progress_bar.setValue(value)

    def parsing_complete(self, results):
        """Handle completed parsing"""
        self.status_label.setText(f"Completed processing {len(results)} resumes.")
        self.log_text.append(f"Successfully AI processed {len(results)} resumes.")

        # Save to database
        try:
            self.db_manager.save_resume_data(results)
            self.log_text.append("Saved all resume data to database.")

            # Refresh results table
            self.load_all_resumes()

        except Exception as e:
            self.log_text.append(f"Error saving to database: {str(e)}")

        self.process_button.setEnabled(True)

    def basic_processing_complete(self, results):
        """Handle completed basic processing."""
        self.status_label.setText(f"Completed basic processing for {len(results)} resumes.")
        self.log_text.append(f"Successfully basic processed {len(results)} resumes (raw text and file info saved).")

        # Save to database
        try:
            self.db_manager.save_resume_data(results) # Should handle the minimal data
            self.log_text.append("Saved basic resume data to database.")
            self.load_all_resumes() # Refresh results table
        except Exception as e:
            self.log_text.append(f"Error saving basic data to database: {str(e)}")

        self.process_button.setEnabled(True)


    def parsing_error(self, error_msg):
        """Handle parsing errors"""
        self.log_text.append(f"Error: {error_msg}")
        self.status_label.setText("Error occurred during processing.")
        self.process_button.setEnabled(True)

    def confirm_clear_database(self):
        """Confirm and clear the entire database."""
        reply = QMessageBox.question(self, 'Confirm Clear Database',
                                     "Are you sure you want to delete ALL resumes and their data from the database?\nThis action cannot be undone.",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.log_text.append("Attempting to clear the entire database...")
            success, message = self.db_manager.clear_all_data()

            if success:
                self.log_text.append(message) # Log success message from db_manager
                self.status_label.setText("Database cleared successfully.")
                QMessageBox.information(self, "Database Cleared", message)
                self.results_table.setRowCount(0) # Clear the search results table
                self.search_input.clear() # Clear search inputs
                self.boolean_search_input.clear()
                self.search_status_label.setText("Database cleared. Perform a new search or load all.")
                self.last_search_query = "N/A (Database Cleared)"
            else:
                self.log_text.append(f"Failed to clear database: {message}") # Log failure
                self.status_label.setText("Database clear failed.")
                QMessageBox.critical(self, "Database Error", message)

    def optimize_database_performance(self):
        """Optimize database for better performance."""
        self.log_text.append("Starting database optimization...")
        self.status_label.setText("Optimizing database...")

        # Disable the button during optimization
        sender = self.sender()
        if sender:
            sender.setEnabled(False)
            sender.setText("Optimizing...")

        try:
            success, message = self.db_manager.optimize_database()

            if success:
                self.log_text.append(message)
                self.status_label.setText("Database optimization completed.")
                QMessageBox.information(self, "Optimization Complete",
                                      "Database has been optimized for better performance.\n\n"
                                      "Benefits:\n"
                                      "• Faster search queries\n"
                                      "• Improved FTS5 full-text search\n"
                                      "• Optimized indexes\n"
                                      "• Reduced database size")
            else:
                self.log_text.append(f"Database optimization failed: {message}")
                self.status_label.setText("Database optimization failed.")
                QMessageBox.warning(self, "Optimization Failed", f"Database optimization failed:\n{message}")

        except Exception as e:
            error_msg = f"Unexpected error during optimization: {str(e)}"
            self.log_text.append(error_msg)
            self.status_label.setText("Database optimization failed.")
            QMessageBox.critical(self, "Optimization Error", error_msg)

        finally:
            # Re-enable the button
            if sender:
                sender.setEnabled(True)
                sender.setText("Optimize Database")

    def show_database_statistics(self):
        """Show database performance statistics."""
        try:
            stats = self.db_manager.get_database_stats()

            if not stats:
                QMessageBox.warning(self, "Statistics Error", "Could not retrieve database statistics.")
                return

            # Format statistics for display
            stats_text = f"""Database Performance Statistics:

📊 Data Overview:
• Total Resumes: {stats.get('total_resumes', 0):,}
• Resume Data Records: {stats.get('total_resume_data', 0):,}• Database Size: {stats.get('db_size_mb', 0):.2f} MB ({stats.get('db_size_bytes', 0):,} bytes)

🔍 Search Optimization:
• FTS5 Full-Text Search: {'✅ Enabled' if stats.get('fts_enabled', False) else '❌ Disabled'}
• FTS Entries: {stats.get('fts_entries', 0):,}
• Custom Indexes: {stats.get('custom_indexes', 0)}

💡 Performance Tips:
• For 100k+ resumes, ensure appropriate B-Tree indexes are created for optimal search speed.
• Run "Optimize Database" periodically to maintain performance
• Consider using boolean search for complex queries"""

            # Create a dialog to show statistics
            dialog = QDialog(self)
            dialog.setWindowTitle("Database Statistics")
            dialog.setMinimumSize(500, 400)

            layout = QVBoxLayout(dialog)

            text_widget = QTextEdit()
            text_widget.setPlainText(stats_text)
            text_widget.setReadOnly(True)
            layout.addWidget(text_widget)

            button_layout = QHBoxLayout()

            refresh_button = QPushButton("Refresh")
            refresh_button.clicked.connect(lambda: self._refresh_stats(text_widget))
            button_layout.addWidget(refresh_button)

            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            dialog.exec_()

        except Exception as e:
            error_msg = f"Error retrieving database statistics: {str(e)}"
            self.log_text.append(error_msg)
            QMessageBox.critical(self, "Statistics Error", error_msg)

    def _refresh_stats(self, text_widget):
        """Refresh statistics in the dialog."""
        try:
            stats = self.db_manager.get_database_stats()

            stats_text = f"""Database Performance Statistics:

📊 Data Overview:
• Total Resumes: {stats.get('total_resumes', 0):,}
• Resume Data Records: {stats.get('total_resume_data', 0):,}• Database Size: {stats.get('db_size_mb', 0):.2f} MB ({stats.get('db_size_bytes', 0):,} bytes)

🔍 Search Optimization:
• FTS5 Full-Text Search: {'✅ Enabled' if stats.get('fts_enabled', False) else '❌ Disabled'}
• FTS Entries: {stats.get('fts_entries', 0):,}
• Custom Indexes: {stats.get('custom_indexes', 0)}

💡 Performance Tips:
• For 100k+ resumes, ensure appropriate B-Tree indexes are created for optimal search speed.
• Run "Optimize Database" periodically to maintain performance
• Consider using boolean search for complex queries"""

            text_widget.setPlainText(stats_text)

        except Exception as e:
            text_widget.setPlainText(f"Error refreshing statistics: {str(e)}")

    def cancel_search(self):
        """Cancel the current search operation"""
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.cancel()
            self.search_worker.wait(3000)  # Wait up to 3 seconds for thread to finish
            if self.search_worker.isRunning():
                self.search_worker.terminate()  # Force terminate if needed
            self.log_text.append("Search operation cancelled by user.")

        self._hide_search_progress()
        self.search_status_label.setText("Search cancelled.")

    def _show_search_progress(self):
        """Show progress bar and cancel button"""
        self.search_progress_bar.setVisible(True)
        self.search_cancel_button.setVisible(True)
        self.search_progress_bar.setValue(0)

    def _hide_search_progress(self):
        """Hide progress bar and cancel button"""
        self.search_progress_bar.setVisible(False)
        self.search_cancel_button.setVisible(False)

    def _start_search_worker(self, search_type, **kwargs):
        """Start a search worker thread with progress indication"""
        # Cancel any existing search
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.cancel()
            self.search_worker.wait(1000)

        # Create and configure new search worker
        self.search_worker = SearchWorker(self.db_manager, search_type, **kwargs)
        self.search_worker.progress_signal.connect(self.search_progress_bar.setValue)
        self.search_worker.status_signal.connect(self.search_status_label.setText)
        self.search_worker.results_signal.connect(self._handle_search_results)
        self.search_worker.error_signal.connect(self._handle_search_error)
        self.search_worker.finished_signal.connect(self._hide_search_progress)

        # Show progress UI and start worker
        self._show_search_progress()
        self.search_worker.start()

    def _handle_search_results(self, results, highlight_terms):
        """Handle search results from worker thread"""
        # Store highlight terms for preview
        if highlight_terms:
            if isinstance(highlight_terms, list):
                self.last_boolean_search_terms = highlight_terms
                self.current_preview_highlight_terms = list(highlight_terms)
                self._update_boolean_keyword_display(highlight_terms)
                self.log_text.append(f"Boolean search terms for highlighting: {self.last_boolean_search_terms}")
            else:
                # For keyword search, highlight_terms might be the keywords themselves
                self.last_ai_search_keywords = highlight_terms if isinstance(highlight_terms, list) else []
                self.current_preview_highlight_terms = self.last_ai_search_keywords
        else:
            # Clear highlight terms for load all operations
            self.last_boolean_search_terms = []
            self.current_preview_highlight_terms = []
            self._update_boolean_keyword_display([])

        # Display results
        if results:
            self.display_results(results, ranked=False)
            self.log_text.append(f"Search completed. Found {len(results)} results.")
            if highlight_terms:
                self.search_status_label.setText(f"Found {len(results)} results")
            else:
                self.search_status_label.setText(f"Showing all {len(results)} resumes.")
        else:
            self.results_table.setRowCount(0)
            self.log_text.append("Search completed. No results found.")
            self.search_status_label.setText("No results found.")

    def _handle_search_error(self, error_message):
        """Handle search errors from worker thread"""
        self.log_text.append(f"Search error: {error_message}")
        QMessageBox.critical(self, "Search Error", f"An error occurred during search: {error_message}")
        self.results_table.setRowCount(0)

    def search_resumes(self):
        """Search resumes based on input query, always using AI for query analysis."""
        query = self.search_input.text().strip()
        self.last_search_query = query # Store the query
        self.last_boolean_search_terms = [] # Clear boolean terms
        self.last_boolean_search_query = "" # Clear boolean search context
        # self.last_query_analysis will be set by handle_ai_analysis_and_proceed
        # self.current_preview_highlight_terms will be set by handle_ai_analysis_and_proceed

        if not query:
            self._start_search_worker('all')  # Use background thread for loading all
            return

        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Required", "Please enter and save your  API Key in the Settings tab.")
            return

        self.search_status_label.setText("Analyzing search query with AI...")
        self.log_text.append(f"Analyzing search query with AI: '{query}'")
        self.query_analysis_group.setVisible(False) # Hide previous analysis

        # Fetch unique tags from DB to pass to AIQueryAnalyzer
        try:
            db_unique_tags = self.db_manager.get_unique_tags()
        except Exception as e:
            self.log_text.append(f"Error fetching unique tags from DB: {e}. Proceeding without them.")
            db_unique_tags = []
        # Always use AIQueryAnalyzer to understand the query
        self.analyzer_thread = AIQueryAnalyzer(query, api_key, db_unique_tags)
        self.analyzer_thread.complete_signal.connect(
            self.handle_ai_generated_boolean_query
        )
        self.analyzer_thread.error_signal.connect(self.handle_analysis_error) # Renamed for clarity
        self.analyzer_thread.start()

    def perform_boolean_search(self):
        """Perform search based on boolean keyword query."""
        query_text = self.boolean_search_input.text().strip()
        self.last_boolean_search_query = query_text # Store this query
        self.last_search_query = "" # Clear natural language search context
        self.last_ai_search_keywords = [] # Clear AI keywords
        self.query_analysis_group.setVisible(False) # Hide AI analysis from previous (natural lang) search

        if not query_text:
            # If query is empty, load all, and ensure highlight terms are cleared
            self.last_boolean_search_terms = []
            self.current_preview_highlight_terms = []
            self.log_text.append("Boolean search input empty, loading all resumes.")
            self.load_all_resumes()
            self._update_boolean_keyword_display([]) # Clear keyword status display
            return

        self.log_text.append(f"Starting boolean keyword search: '{query_text}'")
        search_raw_text_only = self.search_raw_text_only_checkbox.isChecked()

        # Start background search
        self._start_search_worker('boolean',
                                query_text=query_text,
                                search_raw_text_only=search_raw_text_only)

    def handle_search_input_changed(self, text):
        """Handles dynamic changes to the search input field."""
        if not text.strip(): # If the search box is cleared
            self.log_text.append("Search input cleared, loading all resumes.")
            # self.last_search_query will be updated by load_all_resumes
            self.last_boolean_search_terms = [] # Clear for highlighting
            self.load_all_resumes()
            self.current_preview_highlight_terms = []
            self._update_boolean_keyword_display([]) # Clear keyword status display
            self.last_ai_search_keywords = [] # Clear AI keywords as well
            # No need to explicitly hide query_analysis_group, load_all_resumes does it.

    def handle_boolean_search_input_changed(self, text):
        """Handles dynamic changes to the boolean search input field."""
        if not text.strip(): # If the boolean search box is cleared
            self.log_text.append("Boolean search input cleared, loading all resumes.")
            # self.last_boolean_search_query will be cleared by load_all_resumes indirectly
            # as load_all_resumes sets last_search_query and clears boolean/AI terms.
            self.load_all_resumes()
            self._update_boolean_keyword_display([]) # Clear keyword status display
            self.current_preview_highlight_terms = []
            # No need to explicitly hide query_analysis_group, load_all_resumes does it.

    def handle_ai_analysis_and_proceed(self, analysis, original_query, api_key):
        """
        Handles the result from AIQueryAnalyzer.
        Uses AI-generated keywords for DB search and displays filtered results directly.
        """
        # Display AI query analysis
        self.display_query_analysis(analysis) # Always display analysis from natural language search
        self.query_analysis_group.setVisible(True)
        self.last_query_analysis = analysis # Store the analysis
        self._update_boolean_keyword_display([]) # Clear boolean keyword display
        self.last_boolean_search_terms = [] # Clear boolean terms

        keywords_from_ai = analysis.get("search_keywords")

        if not analysis or keywords_from_ai is None: # AI analysis itself failed or didn't follow format
            self.log_text.append("AI query analysis failed or did not return keywords. Cannot proceed with AI-driven search.")
            self.search_status_label.setText("AI analysis failed to identify search criteria.")
            self.results_table.setRowCount(0)
            return

        self.last_ai_search_keywords = keywords_from_ai if keywords_from_ai else [] # Store AI keywords
        self.current_preview_highlight_terms = list(self.last_ai_search_keywords) # Update for preview

        if not keywords_from_ai: # AI returned an empty list of keywords
            self.log_text.append("AI query analysis returned no specific keywords. No resumes will be matched from the database based on these keywords. Highlighting will use an empty list.")
            self.search_status_label.setText("AI found no specific keywords in your query to filter the database.")
            self.results_table.setRowCount(0)
            # Query analysis group is already made visible by display_query_analysis
            return

        self.log_text.append(f"Using AI-extracted search keywords for DB filtering: {keywords_from_ai}")
        self.search_status_label.setText(f"Filtering database with AI-extracted keywords...")

        # Perform database search using AI-generated keywords
        db_results = self.db_manager.search_resumes(keywords_from_ai)

        if not db_results:
            self.search_status_label.setText("No initial matching resumes found in database based on AI keywords.")
            self.results_table.setRowCount(0)
            return

        # Display filtered results directly (unranked)
        self.log_text.append(f"Displaying {len(db_results)} matches based on AI-extracted keywords.")
        self.search_status_label.setText(f"Showing {len(db_results)} matches based on AI keywords.")
        self.display_results(db_results, ranked=False) # This will update the table


    def display_query_analysis(self, analysis):
        """Display the query analysis results in the UI"""
        # Make the analysis section visible
        self.query_analysis_group.setVisible(True)

        # Update each category label
        for category, label in self.analysis_labels.items():
            if category in analysis:
                category_title = category.replace('_', ' ').title()
                category_data = analysis[category]
                requirements = category_data.get("requirements", [])
                importance = category_data.get("importance", "Low")

                # Color-code importance
                color = "#00BFFF" if importance.lower() == "high" else "#FFA500" if importance.lower() == "medium" else "#9D9D9D" # Adjusted colors

                if requirements:
                    # Format the requirements with importance indicator
                    req_text = f"<b>{category_title}:</b> <span style='color:{color};'>[{importance.upper()}]</span> {', '.join(requirements)}"
                    label.setText(req_text)
                else:
                    label.setText(f"<b>{category_title}:</b> <span style='color:{color};'>[{importance.upper()}]</span> None specified")
            else:
                label.setText("<span style='color:#808080;'>[Low] None specified</span>")

        # Update search keywords
        keywords = analysis.get("search_keywords", [])
        if keywords:
            self.keywords_label.setText(f"<span style='color:#007ACC;'>{', '.join(keywords)}</span>")
        else:
            self.keywords_label.setText("<span style='color:#808080;'>None generated by AI</span>")

    def handle_analysis_error(self, error_msg):
        """Handle query analysis errors"""
        self.log_text.append(f"AI query analysis error: {error_msg}")
        self.search_status_label.setText(f"AI Query analysis error: {error_msg}. Cannot perform search.")
        self.results_table.setRowCount(0)
        self.query_analysis_group.setVisible(False) # Hide analysis group on error

    def load_all_resumes(self):
        """Load all resumes from database and display them without AI ranking"""
        self.log_text.append("Loading all resumes from database...")
        self.query_analysis_group.setVisible(False) # Hide analysis when showing all
        self.last_search_query = "N/A (All resumes loaded)" # Update context
        self.last_boolean_search_terms = [] # Clear for highlighting
        self.last_ai_search_keywords = [] # Clear AI keywords
        self.last_query_analysis = None # No AI analysis when loading all
        self._update_boolean_keyword_display([]) # Clear keyword status display
        self.current_preview_highlight_terms = []

        # Start background loading
        self._start_search_worker('all')

    def handle_ai_generated_boolean_query(self, analysis_result: dict):
        """
        Handles the boolean query and explanation generated by AIQueryAnalyzer
        from a natural language search.
        """
        boolean_query = analysis_result.get("boolean_query")
        explanation = analysis_result.get("explanation")

        self.log_text.append(f"AI Query Analyzer Result: Boolean Query='{boolean_query}', Explanation='{explanation}'")

        if not boolean_query:
            self.log_text.append("AI Query Analyzer did not return a boolean query. Cannot proceed with search.")
            self.search_status_label.setText("AI analysis failed to generate a search query.")
            self.results_table.setRowCount(0)
            if explanation:
                self.log_text.append(f"AI Explanation (no query): {explanation}")
            return

        self.last_boolean_search_query = boolean_query
        self.last_ai_search_keywords = [] # This path uses boolean terms, not separate AI keywords
        self.search_status_label.setText(f"AI Explanation: {explanation[:100]}... | Searching with: {boolean_query}")
        self.log_text.append(f"AI Explanation: {explanation}")

        search_raw_text_only = self.search_raw_text_only_checkbox.isChecked()

        # Start background search with AI-generated boolean query
        self._start_search_worker('boolean',
                                query_text=boolean_query,
                                search_raw_text_only=search_raw_text_only)

        self.query_analysis_group.setVisible(False) # Hide complex analysis group, AIQueryAnalyzer's explanation is simpler

    def _safe_str_for_table_display(self, value, default_val="N/A"):
        """Helper to convert a value to a string for table display, using a default if None or empty."""
        if value is None:
            return default_val
        s_value = str(value).strip()
        return s_value if s_value else default_val



    def _format_field_for_display(self, field_value_json_str):
        """Helper to format JSON list strings or single strings for display. Returns 'N/A' if empty."""
        if not field_value_json_str:  # Handles None, empty string from DB
            return "N/A"

        try:
            # Attempt to parse as JSON
            data = json.loads(field_value_json_str)

            if isinstance(data, list):
                # Filter out empty/whitespace strings from the list before joining
                valid_items = [str(item).strip() for item in data if str(item).strip()]
                if not valid_items: # If list becomes empty after filtering
                    return "N/A"
                return ", ".join(valid_items)
            else:
                # If it parsed as JSON but wasn't a list (e.g., a string like "Baseline", or a number)
                # Convert to string and check if it's empty after stripping
                processed_str = str(data).strip()
                return "N/A" if not processed_str else processed_str

        except (json.JSONDecodeError, TypeError):
            # If it's not valid JSON, treat it as a plain string
            plain_str = str(field_value_json_str).strip()
            return "N/A" if not plain_str else plain_str

    def handle_table_selection_changed(self):
        """Shows or hides the 'Delete Selected Resumes' button based on table selection."""
        selected_items = self.results_table.selectedItems()
        selected_rows_count = len(self.results_table.selectionModel().selectedRows())

        if selected_rows_count > 0:
            self.delete_selected_button.setEnabled(True)
            if selected_rows_count == 1:
                # Single row selected, update preview
                first_selected_row_index = self.results_table.selectionModel().selectedRows()[0]
                row = first_selected_row_index.row()
                id_item = self.results_table.item(row, 0) # Rank column for ID
                if id_item and id_item.data(Qt.UserRole) is not None:
                    resume_id = int(id_item.data(Qt.UserRole))
                    self._update_resume_preview(resume_id)
                else:
                    self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
                    self.preview_placeholder_label.setText("Error: Could not get resume ID for preview.")
                    self._reset_boolean_keyword_status() # Reset statuses if preview fails
            else: # Multiple rows selected
                self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
                self.preview_placeholder_label.setText("Multiple resumes selected. Preview unavailable.")
                self._reset_boolean_keyword_status() # Reset statuses for multiple selection
        else:
            self.delete_selected_button.setEnabled(False)
            self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
            self.preview_placeholder_label.setText("Select a resume from the table to preview.")
            self._reset_boolean_keyword_status() # Reset statuses when no selection
        if selected_rows_count > 1: # Logic for bulk analyse button
            at_least_one_unknown_name = False
            selected_row_indices_list = self.results_table.selectionModel().selectedRows() # QModelIndex list
            for model_index in selected_row_indices_list:
                row = model_index.row()
                name_item = self.results_table.item(row, 2) # Column 2 is 'Name'
                if name_item and name_item.text().strip().lower() == "unknown":
                    at_least_one_unknown_name = True
                    break # Found one, no need to check further

            if at_least_one_unknown_name:
                self.bulk_analyse_button.setEnabled(True)
            else:
                self.bulk_analyse_button.setEnabled(False)
        else:
            self.bulk_analyse_button.setEnabled(False)

    def confirm_delete_selected_resumes(self):
        """Confirms and deletes selected resumes from the database."""
        selected_rows_indices = self.results_table.selectionModel().selectedRows()
        if not selected_rows_indices:
            QMessageBox.information(self, "No Selection", "No resumes selected for deletion.")
            return

        resume_ids_to_delete = []
        resume_names_for_confirmation = []
        for index in selected_rows_indices:
            row = index.row()
            id_item = self.results_table.item(row, 0)  # Assuming ID is in column 0 UserRole
            name_item = self.results_table.item(row, 2) # Name is in column 2
            if id_item and id_item.data(Qt.UserRole) is not None:
                resume_ids_to_delete.append(id_item.data(Qt.UserRole))
                resume_names_for_confirmation.append(name_item.text() if name_item else f"ID: {id_item.data(Qt.UserRole)}")

        if not resume_ids_to_delete:
            QMessageBox.warning(self, "Error", "Could not retrieve IDs for the selected resumes.")
            return

        confirm_msg = f"Are you sure you want to permanently delete the following {len(resume_ids_to_delete)} resume(s)?\n\n"
        for i, name in enumerate(resume_names_for_confirmation):
            if i < 10: # Show up to 10 names
                confirm_msg += f"- {name}\n"
            elif i == 10:
                confirm_msg += f"...and {len(resume_names_for_confirmation) - 10} more.\n"
                break

        reply = QMessageBox.question(self, "Confirm Deletion", confirm_msg,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            success, message = self.db_manager.delete_resumes_by_ids(resume_ids_to_delete)
            self.log_text.append(f"Deletion attempt for {len(resume_ids_to_delete)} resumes: {message}")
            QMessageBox.information(self, "Deletion Result", message)

            self.refresh_search_results_after_edit() # Refresh the table view
            # The delete_selected_button will be hidden by handle_table_selection_changed when selection clears

    def display_results(self, results, ranked=True):
        """Display search results in the table. If ranked is False, hide rank/score."""
        self.results_table.setRowCount(0)
        self.ranked_data = {} # Clear previous ranked data when displaying new non-ranked/ranked results

        if not results:
            # This case should ideally be handled by the caller,
            # but as a safeguard:
            self.search_status_label.setText("No results to display.")
            self.log_text.append("display_results called with no results.")
            return

        for row_idx, result in enumerate(results):
            self.results_table.insertRow(row_idx)

            # Unpack result - fields correspond to the SELECT statement in DatabaseManager
            # Indices: 0:id, 1:filename, 2:upload_date, 3:full_name, 4:email, 5:phone, 6:location,
            # 7:security_clearance_json, 8:attributes_json, 9:objectives_str, 10:skills_json, ...
            (resume_id, filename_raw, _, full_name_raw, email_raw, phone_raw,
             location_raw, security_clearance_json, attributes_json, *other_data) = result

            # Use the helper for simple string fields
            display_full_name = self._safe_str_for_table_display(full_name_raw, default_val="Unknown")
            display_email = self._safe_str_for_table_display(email_raw)
            display_phone = self._safe_str_for_table_display(phone_raw)
            display_location = self._safe_str_for_table_display(location_raw)
            display_filename = self._safe_str_for_table_display(filename_raw, default_val="[No Filename]")

            display_attributes = self._format_field_for_display(attributes_json) # from result[8]
            display_security_clearance = self._format_field_for_display(security_clearance_json) # from result[7]

            # Store data for the details dialog, especially if not ranked (no separate explanation)
            self.ranked_data[row_idx] = {
                'explanation': "N/A (Results not ranked by AI)" if not ranked else "See AI Ranking for explanation.",
                'score': "N/A", # Score is not applicable
                'rank': "N/A",  # Rank is not applicable
                'resume_id': resume_id
            }

            # Set table items (without ranking information if ranked=False)
            rank_val = "" # Placeholder, will be hidden if not ranked
            score_val = "" # Placeholder, will be hidden if not ranked

            # For unranked results, Rank and Score columns are typically hidden or empty.
            # We use standard QTableWidgetItems here as they won't hold meaningful numeric values for sorting.
            rank_item = QTableWidgetItem(rank_val)
            rank_item.setData(Qt.UserRole, resume_id)  # Store resume ID (result[0])
            self.results_table.setItem(row_idx, 0, rank_item)

            score_item = QTableWidgetItem(score_val)
            self.results_table.setItem(row_idx, 1, score_item)
            # If these columns were to be made visible and sortable with empty strings,
            # NumericTableWidgetItem would need to handle empty string in its constructor.

            self.results_table.setItem(row_idx, 2, QTableWidgetItem(display_full_name))

            self.results_table.setItem(row_idx, 3, QTableWidgetItem(display_email))

            self.results_table.setItem(row_idx, 4, QTableWidgetItem(display_phone))

            self.results_table.setItem(row_idx, 5, QTableWidgetItem(display_location))

            clearance_item = QTableWidgetItem(display_security_clearance)
            self.results_table.setItem(row_idx, 6, clearance_item)

            attributes_item = QTableWidgetItem(display_attributes)
            self.results_table.setItem(row_idx, 7, attributes_item)
            self.results_table.setItem(row_idx, 8, QTableWidgetItem(display_filename))

        if not ranked:
            self.results_table.hideColumn(0) # Hide Rank
            self.results_table.hideColumn(1) # Hide Score
            self.search_status_label.setText(f"Displaying {len(results)} filtered (unranked) results.")
            self.log_text.append(f"Displayed {len(results)} filtered (unranked) results.")
        else:
            # This 'else' branch should ideally not be hit if ranking is removed.
            # If it were, Rank/Score columns would be visible.
            self.search_status_label.setText(f"Displaying {len(results)} results.") # Generic message
            self.log_text.append(f"Displayed {len(results)} results (display_results with ranked=True).")

        # self.results_table.resizeColumnsToContents() # No longer needed due to ResizeToContents/Stretch modes

    def handle_table_item_double_clicked(self, item: QTableWidgetItem):
        """
        Handles double-click events on the results table.
        If the "File" column is double-clicked, previews the resume.
        Otherwise, shows the resume details view.
        """
        row = item.row()
        column = item.column()

        id_item = self.results_table.item(row, 0) # Assuming Rank column (index 0) stores resume_id in UserRole
        if not id_item or id_item.data(Qt.UserRole) is None:
            QMessageBox.warning(self, "Error", "Could not retrieve resume ID.")
            return
        resume_id = int(id_item.data(Qt.UserRole))

        if column == 8: # "File" column (index 8)
            self.log_text.append(f"Double-click on File column for resume ID {resume_id}. Triggering preview.")
            file_path = self.db_manager.get_resume_file_path(resume_id)
            if not file_path:
                QMessageBox.warning(self, "File Path Not Found", f"The resume file path for ID {resume_id} is not available in the database.")
                return
            self.preview_resume_from_path(file_path)
        else: # Double-click on any other column
            self.log_text.append(f"Double-click on column {column} (e.g., Name, Email) for resume ID {resume_id}. Showing details view.")
            # Call the existing method to show the detailed view
            self.show_resume_details(item)


    def show_resume_details(self, item):
        """Show detailed resume information"""
        row = item.row()
        # self.showMaximized() # Maximize application window - user can do this

        # Get resume ID from the UserRole data of the first visible or designated item (e.g., rank or name)
        # Assuming rank_item (column 0) always holds the resume_id in UserRole
        id_item = self.results_table.item(row, 0)
        if not id_item or id_item.data(Qt.UserRole) is None:
            QMessageBox.warning(self, "Error", "Could not retrieve resume ID for details.")
            return
        resume_id = int(id_item.data(Qt.UserRole))

        resume_data_tuple = self.db_manager.get_resume_details_by_id(resume_id)
        if not resume_data_tuple:
            QMessageBox.warning(self, "Error", f"Could not retrieve full details for resume ID {resume_id}.")
            return
        # resume_id is already correctly fetched

        # Get basic info directly from table cells
        rank_text = self.results_table.item(row, 0).text() if self.results_table.isColumnHidden(0) is False else "N/A"
        score_text = self.results_table.item(row, 1).text() if self.results_table.isColumnHidden(1) is False else "N/A"
        full_name = self.results_table.item(row, 2).text()
        # Get explanation from self.ranked_data if available for this row
        explanation = "Explanation not available for this view."
        if hasattr(self, 'ranked_data') and row in self.ranked_data:
            explanation = self.ranked_data[row].get('explanation', explanation)
            # If score/rank were N/A in table but available in ranked_data, update them
            if score_text == "N/A" and self.ranked_data[row].get('score') not in [None, "N/A", ""]:
                score_text = str(self.ranked_data[row]['score'])
            if rank_text == "N/A" and self.ranked_data[row].get('rank') not in [None, "N/A", ""]:
                 rank_text = str(self.ranked_data[row]['rank'])

        # Determine which set of terms to use for highlighting
        terms_for_highlighting = []
        if self.last_ai_search_keywords: # Prioritize AI keywords if they exist from a natural language search
            terms_for_highlighting = self.last_ai_search_keywords
        elif self.last_boolean_search_terms: # Otherwise, use boolean search terms if they exist
            terms_for_highlighting = self.last_boolean_search_terms

        # Create the detail widget
        detail_widget = ResumeDetailWidget(resume_data_tuple,
                                           rank_text,
                                           score_text,
                                           explanation,
                                           main_window_ref=self,
                                           search_terms=terms_for_highlighting) # Pass the correct terms


        # If a detail widget already exists on the stack (page 1), remove it before adding new one
        if self.search_tab_stack.count() > 1:
            old_detail_widget = self.search_tab_stack.widget(1)
            if old_detail_widget:
                self.search_tab_stack.removeWidget(old_detail_widget)
                old_detail_widget.deleteLater() # Important for memory management

        self.search_tab_stack.addWidget(detail_widget)
        self.search_tab_stack.setCurrentWidget(detail_widget)
        self.log_text.append(f"Showing details for: {full_name}")

        # Hide the main tab bar
        self.tabs_widget.tabBar().setVisible(False)


    def preview_resume_from_path(self, file_path):
        """Open the resume file with the default application"""
        # This method is now called by ResumeDetailDialog, passing the file_path
        reconstructed_path = self._reconstruct_path_for_preview(file_path)

        try:
            if not reconstructed_path:
                QMessageBox.warning(self, "File Path Not Available", "The resume file path is not available to preview.")
                return

            # Check if the file exists
            if not os.path.exists(reconstructed_path):
                QMessageBox.warning(self, "File Not Found",
                                    f"The resume file could not be found at the reconstructed location:\n{reconstructed_path}\n"
                                    f"(Original stored path: {file_path})")
                return

            # Open the file with the default application
            if sys.platform == 'win32':
                os.startfile(reconstructed_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(('open', file_path))
            else:  # Linux and other Unix-like systems
                subprocess.call(('xdg-open', file_path))

            self.log_text.append(f"Opened resume file: {os.path.basename(file_path)}")

        except Exception as e:
            QMessageBox.warning(self, "Error Opening File", f"An error occurred while trying to open the resume file:\n{str(e)}")
            self.log_text.append(f"Error opening resume file: {str(e)}")

    def load_app_settings(self):
        """Load saved settings when the application starts."""
        # Load API Key
        saved_api_key = self.settings.value("apiKey", "") # Default to empty string if not found
        if saved_api_key and hasattr(self, 'api_key_input'):
            self.api_key_input.setText(saved_api_key)
            if hasattr(self, 'log_text'): # Check if log_text is initialized
                self.log_text.append("Loaded saved API Key.")

        # Load Database Path
        # self.current_db_path is already set during __init__
        # We just need to update the UI field here.
        if hasattr(self, 'db_path_input'):
            self.db_path_input.setText(self.current_db_path)

        # Load Resume Document Base Path
        saved_doc_base_path = self.settings.value(DOC_BASE_PATH_SETTING_KEY, "")
        if hasattr(self, 'doc_base_path_input'):
            self.doc_base_path_input.setText(saved_doc_base_path)
            self.log_text.append(f"Loaded Resume Document Base Path: {saved_doc_base_path if saved_doc_base_path else 'Not set'}")

        # Load Auto Parse on View setting
        saved_auto_parse = self.settings.value("autoParseOnView", False, type=bool)
        if hasattr(self, 'auto_parse_checkbox'):
            self.auto_parse_checkbox.setChecked(saved_auto_parse)
            self.log_text.append(f"Loaded 'Auto Parse on View' setting: {saved_auto_parse}.")

        # Load Basic Upload Enabled setting
        # Default to True to ensure it's ticked by default if the setting doesn't exist yet.
        saved_basic_upload = self.settings.value("basicUploadEnabled", True, type=bool)
        if hasattr(self, 'basic_upload_checkbox'): # Check if basic_upload_checkbox exists
            self.basic_upload_checkbox.setChecked(saved_basic_upload)
            if hasattr(self, 'log_text'): # Check if log_text is initialized
                self.log_text.append(f"Loaded 'Basic Upload Enabled' setting: {saved_basic_upload}.")

        # Load Auto Convert DOCX to PDF setting
        saved_auto_convert_docx = self.settings.value("autoConvertDocxToPdf", False, type=bool) # Default to False
        if hasattr(self, 'auto_convert_docx_checkbox'):
            self.auto_convert_docx_checkbox.setChecked(saved_auto_convert_docx)
            if hasattr(self, 'log_text'):
                self.log_text.append(f"Loaded 'Auto Convert DOCX to PDF' setting: {saved_auto_convert_docx}.")

    def save_api_key_setting(self):
        """Save the API key to QSettings."""
        if hasattr(self, 'api_key_input'):
            api_key = self.api_key_input.text().strip()
            self.settings.setValue("apiKey", api_key)
            QMessageBox.information(self, "API Key Saved", " API Key has been saved. It will be used for future sessions.")
            if hasattr(self, 'log_text'):
                self.log_text.append(" API Key saved to settings.")

    def browse_db_file(self):
        """Open file dialog to select a database file or specify a new one."""
        # Suggest current path's directory or user's documents folder as starting point
        current_path_val = self.db_path_input.text()
        start_dir = os.path.dirname(current_path_val) if current_path_val and os.path.exists(os.path.dirname(current_path_val)) else os.path.expanduser("~")

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Select or Create Database File", start_dir, "Database Files (*.db);;All Files (*)"
        )
        if file_path:
            if not file_path.lower().endswith(".db"):
                file_path += ".db" # Ensure .db extension
            self.db_path_input.setText(os.path.normpath(file_path))
            self.log_text.append(f"Database path selected in UI: {file_path}")

    def save_db_path_and_prompt_restart(self):
        """Save the database path to QSettings and prompt for restart."""
        new_db_path = self.db_path_input.text().strip()
        if not new_db_path:
            QMessageBox.warning(self, "Invalid Path", "Database path cannot be empty.")
            return

        self.settings.setValue(DB_PATH_SETTING_KEY, new_db_path)
        self.log_text.append(f"New database path saved: {new_db_path}. Restart required.")
        QMessageBox.information(self, "Database Path Saved",
                                f"The database path has been saved to:\n{new_db_path}\n\nPlease restart the application for the change to take effect.")

    def browse_resume_doc_folder(self):
        """Open folder dialog to select the base directory for resume documents."""
        current_path = self.doc_base_path_input.text()
        start_dir = current_path if current_path and os.path.isdir(current_path) else os.path.expanduser("~")

        folder_path = QFileDialog.getExistingDirectory(self, "Select Resume Document Base Folder", start_dir)
        if folder_path:
            self.doc_base_path_input.setText(os.path.normpath(folder_path))
            self.log_text.append(f"Resume document base folder selected in UI: {folder_path}")

    def save_resume_doc_base_path_setting(self):
        """Save the Resume Document Base Path to QSettings."""
        doc_base_path = self.doc_base_path_input.text().strip()
        self.settings.setValue(DOC_BASE_PATH_SETTING_KEY, doc_base_path)
        self.log_text.append(f"Resume Document Base Path saved: {doc_base_path if doc_base_path else 'Cleared'}")
        QMessageBox.information(self, "Document Base Path Saved", f"The Resume Document Base Path has been saved to:\n{doc_base_path if doc_base_path else 'Not set (cleared)'}\nThis path will be used for locating resume files for preview.")

    def save_auto_parse_setting(self):
        """Save the 'Automatically parse basic resumes on view' setting."""
        if hasattr(self, 'auto_parse_checkbox'):
            auto_parse_state = self.auto_parse_checkbox.isChecked()
            self.settings.setValue("autoParseOnView", auto_parse_state)
            self.log_text.append(f"'Auto Parse on View' setting saved: {auto_parse_state}.")

    def save_basic_upload_setting(self):
        """Save the 'Basic Upload Enabled' setting."""
        if hasattr(self, 'basic_upload_checkbox'): # Check if the checkbox exists
            basic_upload_state = self.basic_upload_checkbox.isChecked()
            self.settings.setValue("basicUploadEnabled", basic_upload_state)
            if hasattr(self, 'log_text'):
                 self.log_text.append(f"'Basic Upload Enabled' setting saved: {basic_upload_state}.")
            else:
                # Fallback if log_text is not available for some reason during settings save
                print(f"Log: 'Basic Upload Enabled' setting saved: {basic_upload_state}.")

    def save_auto_convert_docx_setting(self):
        """Save the 'Automatically convert DOCX to PDF' setting."""
        if hasattr(self, 'auto_convert_docx_checkbox'):
            auto_convert_state = self.auto_convert_docx_checkbox.isChecked()
            self.settings.setValue("autoConvertDocxToPdf", auto_convert_state)
            if hasattr(self, 'log_text'):
                self.log_text.append(f"'Auto Convert DOCX to PDF' setting saved: {auto_convert_state}.")


    def show_search_results_view(self):
        """Switches the search tab's QStackedWidget back to the search/results page (index 0)."""
        if hasattr(self, 'search_tab_stack'):
            self.search_tab_stack.setCurrentIndex(0)
            # The old detail widget is removed when a new one is shown,
            # so no explicit removal needed here unless desired for immediate memory release.

            # Show the main tab bar
            self.tabs_widget.tabBar().setVisible(True)

    def on_tab_switched(self, index):
        """Called when the current tab in the QTabWidget changes."""
        current_tab_widget = self.tabs_widget.widget(index) # Renamed for clarity

        if current_tab_widget == self.search_results_tab_widget:
            self.log_text.append("Switched to Search & Results tab.") # Adjusted log message
            # When switching TO the Search & Results tab, ensure the main tab bar is visible.
            # This is because the ResumeDetailWidget (which is part of the search_results_tab_widget's stack)
            # hides the main tab bar. If the user switches away from Search & Results while a detail
            # view is open, and then switches back, the tab bar should reappear.
            self.tabs_widget.tabBar().setVisible(True)

            # --- The Fix: Do NOT reset the view or data ---
            # The existing state of self.search_tab_stack (either results list or detail view)
            # and the data in self.results_table (and search inputs) should persist.
            # We no longer call self.search_tab_stack.setCurrentIndex(0) or self.load_all_resumes() here.
        elif current_tab_widget.layout().parentWidget().windowTitle() == "Upload & Process": # A bit indirect way to check tab by title if needed
            self.log_text.append("Switched to Upload & Process tab.")
        # Add similar elif for Settings tab if specific actions are needed upon switching to it.

    def _initialize_search_results_on_startup(self):
        """Loads data for the Search & Results tab when the application starts."""
        # Check if the current tab is indeed the Search & Results tab
        # (it should be, as it's set as the first tab)
        if self.tabs_widget.currentWidget() == self.search_results_tab_widget:
            self.log_text.append("Application started. Loading initial data for Search & Results tab...")
            # Ensure ResumeParser's helper methods are available if BasicResumeProcessor needs them
            # This is a temporary workaround for BasicResumeProcessor to access ResumeParser's methods.
            if not hasattr(self, '_temp_resume_parser_for_utils'): # auto_convert_docx_to_pdf defaults to False for this util instance
                self._temp_resume_parser_for_utils = ResumeParser([], "", self.db_manager, auto_convert_docx_to_pdf=False)
            # Default behavior on startup is to load all resumes.
            self.last_ai_search_keywords = [] # Clear AI keywords
            self.last_boolean_search_terms = [] # Clear highlighting terms
            self.load_all_resumes()
        else:
            self.log_text.append("Application started. Search & Results is not the initial tab. Data not pre-loaded.")

    def refresh_search_results_after_edit(self):
        """Refreshes the search results table. Re-runs the last search or loads all."""
        self.log_text.append("Refreshing search results after edit...")
        if self.last_boolean_search_query:
            self.perform_boolean_search() # Re-run last boolean search
        elif self.last_search_query and self.last_search_query != "N/A (All resumes loaded)" and self.last_search_query != "N/A (Database Cleared)":
            self.search_resumes() # Re-run last natural language search
        else:
            self.load_all_resumes() # Default to loading all

    def start_data_correction_process(self):
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Required", "Please enter and save your  API Key in the Settings tab.")
            return

        reply = QMessageBox.question(self, 'Confirm Data Correction',
                                     "This will identify resumes with phone numbers or locations that appear unformatted or incomplete, and then use AI to attempt corrections for these specific entries. This process may take some time and consume API credits.\n\nAre you sure you want to proceed?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No:
            return

        self.fix_data_button.setEnabled(False)
        self.data_correction_status_label.setText("Starting data correction process...")
        self.log_text.append("Starting AI batch data correction for phone numbers and locations.")

        self.data_correction_thread = DataCorrectionAIProcessor(api_key, self.db_manager)
        self.data_correction_thread.status_update_signal.connect(lambda msg: [self.data_correction_status_label.setText(msg), self.log_text.append(msg)])
        self.data_correction_thread.complete_signal.connect(self.handle_data_correction_complete)
        self.data_correction_thread.error_signal.connect(lambda err: [self.data_correction_status_label.setText(f"Error: {err}"), self.log_text.append(f"Data Correction Error: {err}"), self.fix_data_button.setEnabled(True)])
        self.data_correction_thread.start()

    def handle_data_correction_complete(self, corrected_count, error_count, total_processed):
        msg = f"Data correction complete. Processed: {total_processed}, Successfully corrected: {corrected_count}, Errors: {error_count}."
        self.data_correction_status_label.setText(msg)
        self.log_text.append(msg)
        self.fix_data_button.setEnabled(True)
        QMessageBox.information(self, "Data Correction Finished", msg + "\nConsider refreshing your search results if any are currently displayed.")
        # self.load_all_resumes() # Optionally refresh the view, or let user do it.

    def bulk_ai_analyse_selected_resumes(self):
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "API Key Required", "Please enter and save your API Key in the Settings tab.")
            return

        selected_rows_indices = self.results_table.selectionModel().selectedRows()
        if len(selected_rows_indices) <= 1: # Should be caught by button enablement, but double check
            QMessageBox.information(self, "Selection Required", "Please select more than one resume for bulk analysis.")
            return

        resumes_to_process_details = [] # List of (resume_id, raw_text, original_filename)
        resumes_names_for_confirmation = []

        for index in selected_rows_indices:
            row = index.row()
            id_item = self.results_table.item(row, 0)
            name_item = self.results_table.item(row, 2) # Name for confirmation

            if id_item and id_item.data(Qt.UserRole) is not None:
                resume_id = int(id_item.data(Qt.UserRole))
                full_resume_data_tuple = self.db_manager.get_resume_details_by_id(resume_id)
                if full_resume_data_tuple:
                    raw_text = full_resume_data_tuple[16]
                    original_filename = full_resume_data_tuple[1]
                    resumes_to_process_details.append((resume_id, raw_text, original_filename))
                    resumes_names_for_confirmation.append(name_item.text() if name_item else f"ID: {resume_id}")
                else:
                    self.log_text.append(f"Warning: Could not fetch details for resume ID {resume_id} for bulk analysis. Skipping.")
            else:
                 self.log_text.append(f"Warning: Could not get resume ID from table row {row} for bulk analysis. Skipping.")

        if not resumes_to_process_details:
            QMessageBox.information(self, "No Resumes", "No valid resumes found to process from selection.")
            return

        confirm_msg = (f"This will perform AI analysis and update details for {len(resumes_to_process_details)} selected resume(s). "
                       "This may take some time and consume API credits.\n\nResumes to process:\n")
        for i, name in enumerate(resumes_names_for_confirmation):
            if i < 5: confirm_msg += f"- {name}\n"
            elif i == 5: confirm_msg += f"...and {len(resumes_names_for_confirmation) - 5} more.\n"
        confirm_msg += "\nDo you want to proceed?"

        reply = QMessageBox.question(self, "Confirm Bulk AI Analysis", confirm_msg,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No:
            return

        self.log_text.append(f"Starting bulk AI analysis for {len(resumes_to_process_details)} resumes.")
        self.search_status_label.setText(f"Bulk AI analysis started for {len(resumes_to_process_details)} resumes...")
        self.bulk_analyse_button.setEnabled(False)
        self.delete_selected_button.setEnabled(False)

        self.bulk_updater_thread = BatchResumeAIUpdater(resumes_to_process_details, api_key, self.db_manager)
        self.bulk_updater_thread.progress_signal.connect(self.handle_bulk_update_progress)
        self.bulk_updater_thread.complete_signal.connect(self.handle_bulk_update_complete)
        self.bulk_updater_thread.status_message_signal.connect(self.handle_bulk_update_status_message) # New connection
        self.bulk_updater_thread.error_signal.connect(self.handle_bulk_update_error)
        self.bulk_updater_thread.start()

    def handle_bulk_update_progress(self, percentage):
        self.search_status_label.setText(f"Bulk AI analysis in progress: {percentage}% complete...")

    def handle_bulk_update_complete(self, updated_resumes_data):
        successful_updates = 0
        failed_updates = 0

        if not updated_resumes_data:
            self.log_text.append("Bulk AI analysis thread completed but returned no data. No updates made.")
            QMessageBox.information(self, "Bulk Analysis", "Bulk AI analysis finished, but no data was returned for updates.")
        else:
            self.log_text.append(f"Bulk AI analysis thread complete. Received data for {len(updated_resumes_data)} resumes. Updating database...")
            for resume_id, parsed_data in updated_resumes_data:
                success, message = self.db_manager.update_full_resume_data(resume_id, parsed_data)
                if success:
                    successful_updates += 1
                else:
                    failed_updates += 1
                    self.log_text.append(f"Failed to update resume ID {resume_id} after bulk AI analysis: {message}")

            summary_msg = (f"Bulk AI analysis and update finished.\n"
                           f"Successfully updated: {successful_updates} resume(s).\n"
                           f"Failed to update: {failed_updates} resume(s).")
            self.log_text.append(summary_msg)
            QMessageBox.information(self, "Bulk Analysis Complete", summary_msg)

        self.search_status_label.setText(f"Bulk AI analysis complete. Updated: {successful_updates}, Failed: {failed_updates}.")
        self.refresh_search_results_after_edit()

    def handle_bulk_update_error(self, error_msg):
        self.log_text.append(f"Error during bulk AI analysis: {error_msg}")
        self.search_status_label.setText(f"Bulk AI analysis error: {error_msg}")
        QMessageBox.critical(self, "Bulk Analysis Error", f"An error occurred during bulk AI analysis: {error_msg}")
        self.load_all_resumes() # Optionally refresh the view

    def handle_bulk_update_error(self, error_msg):
        self.log_text.append(f"Error during bulk AI analysis: {error_msg}")
        self.search_status_label.setText(f"Bulk AI analysis error: {error_msg}")
        QMessageBox.critical(self, "Bulk Analysis Error", f"An error occurred during bulk AI analysis: {error_msg}")
        self.load_all_resumes() # Optionally refresh the view
    def _create_highlighted_html_from_plain_text(self, text_content, terms_to_highlight):
        """
        Takes plain text and a list of terms, returns HTML with terms highlighted.
        Similar to ResumeDetailWidget._highlight_text.
        """
        if not text_content: # Handle None or empty string input
            return ""
        if not terms_to_highlight:
            return html.escape(str(text_content))

        escaped_text = html.escape(str(text_content))

        sorted_original_terms = sorted(terms_to_highlight, key=len, reverse=True)

        processed_terms_for_regex = []
        for term in sorted_original_terms:
            escaped_term_for_regex = re.escape(term)
            if ' ' in term: # Match phrases as is
                processed_terms_for_regex.append(escaped_term_for_regex)
            else: # Match single words with word boundaries
                processed_terms_for_regex.append(r'\b' + escaped_term_for_regex + r'\b')

        if not processed_terms_for_regex: # Should not happen if terms_to_highlight was not empty
            return escaped_text
        pattern = re.compile(f"({'|'.join(processed_terms_for_regex)})", re.IGNORECASE)
        return pattern.sub(r'<span style="background-color: yellow; color: black;">\1</span>', escaped_text)

    def _update_resume_preview(self, resume_id):
        file_path_from_db = self.db_manager.get_resume_file_path(resume_id)
        if not file_path_from_db:
            self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
            self.preview_placeholder_label.setText(f"Preview not available: File path not in DB for resume ID {resume_id}.")
            self._reset_boolean_keyword_status()
            self.log_text.append(f"Preview error: File path not found in DB for resume ID {resume_id}.")
            return

        reconstructed_file_path = self._reconstruct_path_for_preview(file_path_from_db)

        # Fetch raw text for keyword status checking
        resume_data_tuple = self.db_manager.get_resume_details_by_id(resume_id)
        if not reconstructed_file_path or not os.path.exists(reconstructed_file_path):
            self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
            self.preview_placeholder_label.setText(f"Preview not available: File not found for resume ID {resume_id} after path reconstruction.")
            self.log_text.append(f"Preview error: File not found for resume ID {resume_id}. Stored path: '{file_path_from_db}', Reconstructed: '{reconstructed_file_path}'.")
            return

        raw_text_content = None
        if resume_data_tuple and len(resume_data_tuple) > 16:
            raw_text_content = resume_data_tuple[16]

        if raw_text_content:
            self._update_boolean_keyword_statuses(raw_text_content)
        else:
            self.log_text.append(f"Raw text not available for resume ID {resume_id}. Cannot update keyword statuses.")
            self._reset_boolean_keyword_status() # Reset if no raw text


        _, file_ext = os.path.splitext(reconstructed_file_path)
        file_ext = file_ext.lower()

        self.log_text.append(f"Attempting to preview: {os.path.basename(reconstructed_file_path)} (Reconstructed path: {reconstructed_file_path}, Stored: {file_path_from_db})")
        self.log_text.append(f"Highlight terms for preview: {self.current_preview_highlight_terms}")

        if file_ext == ".pdf":
            self._render_pdf_preview(reconstructed_file_path, self.current_preview_highlight_terms)
        elif file_ext == ".docx":
            self._render_docx_preview(reconstructed_file_path, self.current_preview_highlight_terms)
        elif file_ext == ".doc":
            self._render_doc_preview(reconstructed_file_path, self.current_preview_highlight_terms)
        else:
            self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
            self.preview_placeholder_label.setText(f"Preview not supported for this file type: {file_ext}")
            self._reset_boolean_keyword_status() # Also reset if preview type not supported

    def _render_pdf_preview(self, file_path, terms_to_highlight):
        self._terms_for_current_web_load = list(terms_to_highlight) # Store for loadFinished
        self.preview_web_view.setUrl(QUrl.fromLocalFile(file_path))
        self.preview_stacked_widget.setCurrentWidget(self.preview_web_view)
        self.log_text.append(f"Preview: PDF file '{os.path.basename(file_path)}' (Path: {file_path}) loaded for native view.")

    def _render_docx_preview(self, file_path, terms_to_highlight):
        try:
            abs_file_path = os.path.abspath(file_path) # Ensure we have an absolute path for dirname
            if not os.path.exists(abs_file_path):
                self.log_text.append(f"Preview Error: DOCX file not found at {abs_file_path}. Stored path was {file_path}")
                raise FileNotFoundError(f"DOCX file not found at {abs_file_path}")
            # Store terms for findText
            self._terms_for_current_web_load = list(terms_to_highlight)

            with open(file_path, "rb") as docx_file:
                result = mammoth.convert_to_html(docx_file)
                html_output = result.value

                if result.messages:
                    self.log_text.append(f"Mammoth conversion messages for {os.path.basename(file_path)}:")
                    for message in result.messages:
                        self.log_text.append(f"  - Type: {message.type}, Message: {message.message}")

            self.preview_web_view.setHtml(html_output, QUrl.fromLocalFile(os.path.dirname(abs_file_path) + os.path.sep))
            self.preview_stacked_widget.setCurrentWidget(self.preview_web_view)
            self.log_text.append(f"Preview: DOCX file '{os.path.basename(file_path)}' (Path: {file_path}) converted to HTML and displayed.")
            self.log_text.append("Note: DOCX to HTML conversion by Mammoth prioritizes semantic content. Exact visual fidelity may differ.")
        except Exception as e:
            self.log_text.append(f"Error converting DOCX to HTML for preview: {e}. Falling back to plain text.")
            try:
                # Fallback: extract plain text and highlight that
                plain_text_content = self._temp_resume_parser_for_utils.extract_text_from_docx(file_path)
                if plain_text_content is None:
                    raise ValueError("Fallback DOCX text extraction failed.")
                highlighted_html = self._create_highlighted_html_from_plain_text(plain_text_content, terms_to_highlight)
                self.preview_text_edit.setHtml(highlighted_html) # Use QTextEdit for this fallback
                self.preview_stacked_widget.setCurrentWidget(self.preview_text_edit)
                self._terms_for_current_web_load = [] # Manually highlighted, so clear terms for web view
            except Exception as e_text:
                self.log_text.append(f"Error extracting plain text from DOCX as fallback: {e_text}")
                self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
                self.preview_placeholder_label.setText(f"Error previewing DOCX file '{os.path.basename(file_path)}'.\nCould not convert to HTML or extract text.")
                self._terms_for_current_web_load = []

    def _render_doc_preview(self, file_path, terms_to_highlight):
        base_name = os.path.basename(file_path)

        self.log_text.append(f"Preview for .doc file '{base_name}' (Path: {file_path}) is disabled. Users should open .doc files externally.")
        self.preview_stacked_widget.setCurrentWidget(self.preview_placeholder_label)
        self.preview_placeholder_label.setText(
            f"Direct preview for .doc files ('{base_name}') is not supported!"
        )
        # Clear any terms that might have been set for web loading from a previous preview
        self._terms_for_current_web_load = []


    def handle_bulk_update_status_message(self, message: str):
        """Handles informational status messages from the bulk updater thread."""
        self.log_text.append(message)
        self.search_status_label.setText(message) # Or a more summarized version

    def _on_preview_load_finished(self, success):
        """Logs the result of a QWebEngineView load operation for previews."""
        current_url_obj = self.preview_web_view.url()
        current_url_str = current_url_obj.toLocalFile() if current_url_obj.isLocalFile() else current_url_obj.toString()

        if not current_url_str: # Could be if setHtml was used and base URL was not very descriptive
            current_url_display = "HTML content"
        else:
            current_url_display = os.path.basename(current_url_str) if current_url_obj.isLocalFile() else current_url_str

        if success:
            self.log_text.append(f"Preview Pane: Successfully loaded content for '{current_url_display}'.")

            if hasattr(self, '_terms_for_current_web_load') and self._terms_for_current_web_load:
                # Introduce a small delay before attempting to find text
                # This can help if the JS engine isn't fully ready immediately after loadFinished
                QTimer.singleShot(100, self._delayed_find_text) # 100 ms delay
            # else:
                # self.log_text.append("No terms to highlight via findText (likely pre-highlighted HTML).")
        else:
            self.log_text.append(f"Preview Pane: Failed to load content for '{current_url_display}'. URL: {current_url_str}")

    def _delayed_find_text(self):
        """Performs findText operations after a short delay."""
        # Check if the web view still exists and if there are terms
        if not self.preview_web_view or not hasattr(self, '_terms_for_current_web_load') or not self._terms_for_current_web_load:
            self.log_text.append("Delayed findText: Web view or terms no longer available.")
            return

        # Ensure the page is valid
        if not self.preview_web_view.page():
            self.log_text.append("Delayed findText: Web view page is not valid.")
            return

        try:
            # Clear previous highlights first by searching for an empty string
            self.preview_web_view.findText("", QWebEnginePage.FindFlags()) # Clears selection

            flags = QWebEnginePage.FindHighlightAllResults
            # Note: findText with FindHighlightAllResults will highlight all occurrences
            # of the *current term*. If called in a loop for multiple different terms,
            # it's likely that only the highlights for the *last term* processed will persist
            # if the highlighting mechanism isn't additive for different search strings.
            # The terms in self._terms_for_current_web_load are typically sorted alphabetically.

            for term in self._terms_for_current_web_load:
                if term and term.strip(): # Ensure term is not None and not just whitespace
                    if not self.preview_web_view or not self.preview_web_view.page(): break # Re-check validity
                    self.preview_web_view.findText(term, flags)

            if self._terms_for_current_web_load:
                self.log_text.append(f"Preview highlighting attempted for terms: {self._terms_for_current_web_load} using FindHighlightAllResults.")
        except Exception as e:
            self.log_text.append(f"Error during delayed findText operation: {e}")

    def _check_term_in_text(self, term, text_content):
        if not term or not text_content:
            return False
        try:
            escaped_term = re.escape(term)
            if ' ' in term.strip():
                pattern = escaped_term
            else:
                pattern = r'\b' + escaped_term + r'\b'

            if re.search(pattern, text_content, re.IGNORECASE):
                return True
        except re.error:
            self.log_text.append(f"Regex error checking term '{term}' in _check_term_in_text")
            return False
        return False

    def _update_boolean_keyword_display(self, terms):
        # Clear existing keyword widgets
        for widget in self.boolean_keyword_widgets:
            self.boolean_keyword_status_layout.removeWidget(widget)
            widget.deleteLater()
        self.boolean_keyword_widgets = []

        if terms:
            for term in terms:
                widget = KeywordStatusWidget(term)
                self.boolean_keyword_widgets.append(widget)
                self.boolean_keyword_status_layout.addWidget(widget)
            self.keyword_status_scroll_area.setVisible(True)
        else:
            self.keyword_status_scroll_area.setVisible(False)

    def _update_boolean_keyword_statuses(self, raw_text_content):
        if not self.boolean_keyword_widgets: # No keywords displayed (e.g., not a boolean search)
            return

        if not raw_text_content:
            self.log_text.append("Raw text is empty, resetting keyword statuses.")
            self._reset_boolean_keyword_status()
            return

        for kw_widget in self.boolean_keyword_widgets:
            term = kw_widget.term_text # Assuming KeywordStatusWidget stores the original term
            found = self._check_term_in_text(term, raw_text_content)
            kw_widget.setStatus(found)

    def _reset_boolean_keyword_status(self):
        for widget in self.boolean_keyword_widgets:
            widget.resetStatus()

    def closeEvent(self, event):
        """Handle application close event with proper thread cleanup"""
        # Cancel any running search operations
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.cancel()
            self.search_worker.wait(2000)  # Wait up to 2 seconds
            if self.search_worker.isRunning():
                self.search_worker.terminate()

        # Cancel any running AI analyzer operations
        if hasattr(self, 'analyzer_thread') and self.analyzer_thread.isRunning():
            self.analyzer_thread.terminate()
            self.analyzer_thread.wait(1000)

        # Cancel any running parser operations
        if hasattr(self, 'parser_thread') and self.parser_thread.isRunning():
            self.parser_thread.terminate()
            self.parser_thread.wait(1000)

        # Cancel any running bulk operations
        if hasattr(self, 'bulk_updater_thread') and self.bulk_updater_thread.isRunning():
            self.bulk_updater_thread.terminate()
            self.bulk_updater_thread.wait(1000)

        # Accept the close event
        event.accept()

class SingleResumeAIParser(QThread):
    """Thread for parsing a single resume using AI, guided by user input."""
    complete_signal = pyqtSignal(int, dict)  # resume_id, processed_ai_data
    error_signal = pyqtSignal(str)

    def __init__(self, resume_id, raw_text, filename, user_name, user_email, user_phone, api_key, db_manager):
        super().__init__()
        self.resume_id = resume_id
        self.raw_text = raw_text
        self.filename = filename # For context if AI needs it, though prompt focuses on text
        self.user_name = user_name
        self.user_email = user_email
        self.user_phone = user_phone
        self.api_key = api_key
        # self.db_manager = db_manager # Not directly used, but ResumeParser instance might need it

        # For using ResumeParser's utility methods like process_resume_data
        self.util_parser = ResumeParser([], api_key, db_manager) # API key and db_manager for consistency
        self.util_parser.error_signal.connect(self.error_signal.emit) # Route errors if its methods emit

    def run(self):
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel('gemini-2.0-flash')

            prompt = self._create_single_parse_prompt()

            response = model.generate_content(prompt)
            ai_data, error = _try_robust_json_loads(response.text)

            if ai_data is not None:
                # Process the data using ResumeParser's method for consistency
                processed_data = self.util_parser.process_resume_data(ai_data)
                # Ensure 'resume_tags' is handled (process_resume_data expects it)
                if 'resume_tags' not in processed_data:
                    processed_data['resume_tags'] = ai_data.get('resume_tags', [])
                self.complete_signal.emit(self.resume_id, processed_data)
            else:
                self.error_signal.emit(f"Failed to parse JSON from AI response for single resume: {error}")

        except Exception as e:
            self.error_signal.emit(f"Error in SingleResumeAIParser: {str(e)}")

    def _create_single_parse_prompt(self):
        # Base instructions from ResumeParser, adapted for single parse and user hints
        base_instructions = self.util_parser.create_batch_prompt([]).split("Here are the resumes to process")[0]

        # Remove batch-specific parts like "/resumesave" and "original_filename" emphasis for mapping
        base_instructions = base_instructions.replace("CRITICALLY IMPORTANT: After EACH resume's complete JSON object, you MUST include the marker \"/resumesave\" on its own new line to indicate\n        the end of that resume's data. This marker is crucial for parsing your response correctly.", "")
        base_instructions = base_instructions.replace("original_filename (MUST be the filename provided in the input 'Filename: ...' for this resume),", "")
        base_instructions = base_instructions.replace("0. Meta Information (include this in your JSON output for each resume):\n           - original_filename: The filename provided as 'Filename: ...' before the resume text. This is crucial for mapping.", "")
        base_instructions = base_instructions.replace("Process each resume independently and provide a complete JSON object for each one.", "Provide a single complete JSON object for the resume.")

        prompt = f"""
You are provided with the raw text of a resume and potentially some pre-filled basic information (Full Name, Email, Phone Number).
Your task is to extract detailed information from the resume text.

User-provided information (use as strong hints or defaults if extraction from text is unclear or yields "Unknown"):
- Full Name Hint: {self.user_name if self.user_name and self.user_name.lower() != 'unknown' else "Attempt to extract from text or filename"}
- Email Hint: {self.user_email if self.user_email else "Attempt to extract from text"}
- Phone Hint: {self.user_phone if self.user_phone else "Attempt to extract from text"}

{base_instructions}

Resume Text (Filename: {self.filename}):
{self.raw_text[:45000]}

Return the response as a single JSON object.
"""
        return prompt

class DataCorrectionAIProcessor(QThread):
    """Thread for batch correcting phone and location data using AI."""
    status_update_signal = pyqtSignal(str)
    complete_signal = pyqtSignal(int, int, int) # corrected_count, error_count, total_processed
    error_signal = pyqtSignal(str)

    def __init__(self, api_key, db_manager):
        super().__init__()
        self.api_key = api_key
        self.db_manager = db_manager

    def _is_phone_suspicious(self, phone_str):
        if not phone_str or phone_str.lower() == "unknown":
            return True # Empty or unknown is suspicious
        # Regex for a reasonably well-formatted E.164-like number
        # Starts with +, followed by 7 to 15 digits.
        if not re.match(r"^\+\d{7,15}$", phone_str):
            return True # Does not match the desired simple E.164 pattern
        return False

    def _is_location_suspicious(self, location_str):
        if not location_str or location_str.lower() == "unknown":
            return True # Empty or unknown is suspicious
        if len(location_str) < 6: # Arbitrary short length, e.g. "NSW" or "Paris"
            return True
        if ',' not in location_str: # Heuristic: well-formatted locations often have commas
            return True
        return False

    def run(self):
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel('gemini-2.0-flash')

            all_candidates = self.db_manager.get_resumes_for_data_correction()
            if not all_candidates:
                self.status_update_signal.emit("No resumes with valid names found in the database to check for data correction.")
                self.complete_signal.emit(0, 0, 0)
                return

            self.status_update_signal.emit(f"Checking {len(all_candidates)} resumes for suspicious phone/location data...")

            resumes_to_correct = []
            for resume_id, phone, location, raw_text in all_candidates:
                if self._is_phone_suspicious(phone) or self._is_location_suspicious(location):
                    resumes_to_correct.append((resume_id, phone, location, raw_text))

            if not resumes_to_correct:
                self.status_update_signal.emit(f"Checked {len(all_candidates)} resumes. No entries with suspicious phone or location data found for AI correction.")
                self.complete_signal.emit(0, 0, 0) # 0 corrected, 0 errors, 0 processed by AI
                return

            total_suspicious_resumes = len(resumes_to_correct)
            self.status_update_signal.emit(f"Found {total_suspicious_resumes} resumes with suspicious phone/location data. Starting AI correction...")

            corrected_count = 0
            error_count = 0
            BATCH_SIZE = 5 # Smaller batch size for this kind of focused task

            for i in range(0, total_suspicious_resumes, BATCH_SIZE):
                batch = resumes_to_correct[i:i + BATCH_SIZE]
                self.status_update_signal.emit(f"Processing batch {i//BATCH_SIZE + 1} of suspicious entries ({min(i + BATCH_SIZE, total_suspicious_resumes)}/{total_suspicious_resumes})...")

                batch_prompt_items = []
                for resume_id, phone, location, raw_text in batch:
                    batch_prompt_items.append({
                        "resume_id": resume_id,
                        "current_phone": phone if phone else "Unknown",
                        "current_location": location if location else "Unknown",
                        "raw_text_summary": raw_text[:3000] if raw_text else "" # Limit summary size
                    })

                prompt = self._create_correction_batch_prompt(batch_prompt_items)

                try:
                    response = model.generate_content(prompt)

                    corrected_items, error = _try_robust_json_loads(response.text)

                    if corrected_items is None or not isinstance(corrected_items, list):
                        err_msg = error if error else "Parsed data is not a list or is null."
                        self.status_update_signal.emit(f"Batch {i//BATCH_SIZE + 1}: JSON parsing error: {err_msg}. Response: {response.text[:200]}")
                        error_count += len(batch)
                        continue

                    # If corrected_items is an empty list, the loop below won't run, which is fine.
                    for item in corrected_items:
                        resume_id = item.get("resume_id")
                        corrected_phone = item.get("corrected_phone", "Unknown")
                        corrected_location = item.get("corrected_location", "Unknown")
                        if resume_id is not None:
                            self.db_manager.update_corrected_phone_location(resume_id, corrected_phone, corrected_location)
                            corrected_count += 1
                        else:
                            self.status_update_signal.emit(f"Warning: AI response missing resume_id in batch {i//BATCH_SIZE + 1}.")
                            error_count +=1 # Count as an error if ID is missing for mapping

                except Exception as api_e:
                    self.status_update_signal.emit(f"Error processing AI request for batch {i//BATCH_SIZE + 1}: {api_e}")
                    error_count += len(batch) # Count all in batch as error

            self.complete_signal.emit(corrected_count, error_count, total_suspicious_resumes)

        except Exception as e:
            self.error_signal.emit(f"Critical error in DataCorrectionAIProcessor: {str(e)}")
            self.complete_signal.emit(0, total_suspicious_resumes if 'total_suspicious_resumes' in locals() else 0, total_suspicious_resumes if 'total_suspicious_resumes' in locals() else 0) # Assume all errored if thread fails

    def _create_correction_batch_prompt(self, resume_data_list):
        # resume_data_list contains dicts with: resume_id, current_phone, current_location, raw_text_summary
        prompt = """
You will be given a list of resumes, each with a 'resume_id', 'current_phone', 'current_location', and a 'raw_text_summary'.
For EACH resume in the list, your task is to:
1.  Analyze the 'current_phone'. Reformat it to a standardized international E.164 format (e.g., "+***********").
    - Remove all spaces, hyphens, parentheses.
    - If a country code (e.g., "+61") is present, preserve it.
    - If it's a local number (e.g., Australian "04xx" becoming "+614xx", US "(*************" becoming "+***********"), convert it.
    - If 'current_phone' is "Unknown" or clearly invalid, try to extract a valid phone number from the 'raw_text_summary'. If found, format it. If not found, the value for 'corrected_phone' should be "Unknown".
2.  Analyze the 'current_location'. Identify a single, primary location (e.g., "City, State, Country" or "City, Country").
    - If 'current_location' is "Unknown" or seems incorrect/too vague, try to extract a more accurate primary location from the 'raw_text_summary'. If found, use that. If not found, the value for 'corrected_location' should be "Unknown" or the best guess from the text.

Return your response as a single JSON array, where each element is an object with the following structure:
{
    "resume_id": <the_resume_id_provided_in_input_for_this_resume>,
    "corrected_phone": "your_corrected_phone_number_string",
    "corrected_location": "your_corrected_location_string"
}

Ensure you provide an object in the array for EACH resume provided in the input list, maintaining the 'resume_id'.

Here is the list of resumes to process:
"""
        prompt += json.dumps(resume_data_list, indent=2)
        return prompt

class BatchResumeAIUpdater(QThread):
    """
    Thread for AI parsing a batch of existing resumes (identified by resume_id,
    using their stored raw_text and original_filename).
    Updates the database with the newly parsed details.
    """
    progress_signal = pyqtSignal(int)    # Percentage of resumes processed in the batch
    complete_signal = pyqtSignal(list)   # List of (resume_id, parsed_data_dict) for successfully processed resumes
    status_message_signal = pyqtSignal(str) # For informational messages during processing
    error_signal = pyqtSignal(str)       # Error messages

    def __init__(self, resumes_to_parse, api_key, db_manager):
        super().__init__()
        # resumes_to_parse: list of (resume_id, raw_text, original_filename)
        self.resumes_to_parse = resumes_to_parse
        self.api_key = api_key
        self.db_manager = db_manager
        # Use ResumeParser's utility methods for consistency in data processing
        self.util_parser = ResumeParser([], api_key, db_manager)
        self.util_parser.error_signal.connect(self.error_signal.emit) # Route errors

    def run(self):
        BATCH_SIZE_AI = 2 # Number of resumes to send to AI in one go
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')

            total_to_process = len(self.resumes_to_parse)
            if total_to_process == 0:
                self.complete_signal.emit([])
                return

            all_parsed_results_for_db_update = [] # List of (resume_id, processed_data_dict)

            for i in range(0, total_to_process, BATCH_SIZE_AI):
                current_batch_input_details = self.resumes_to_parse[i:i + BATCH_SIZE_AI]

                ai_prompt_batch_items = [
                    {'resume_id': rid, 'filename': fname, 'text': rtext[:45000]} # Limit text for AI
                    for rid, rtext, fname in current_batch_input_details
                ]

                if not ai_prompt_batch_items:
                    continue

                batch_prompt_for_ai = self._create_ai_batch_prompt(ai_prompt_batch_items)

                self.status_message_signal.emit(f"Bulk AI Update: Processing AI batch {i//BATCH_SIZE_AI + 1} (resumes {i+1} to {min(i+BATCH_SIZE_AI, total_to_process)} of {total_to_process})")

                response = model.generate_content(batch_prompt_for_ai)

                id_to_original_details_map_for_batch = {
                    rid: {'filename': fname, 'raw_text': rtext}
                    for rid, rtext, fname in current_batch_input_details
                }
                # Pass the raw response text to the parser
                parsed_batch_from_ai = self._parse_ai_batch_response(response.text, id_to_original_details_map_for_batch)
                all_parsed_results_for_db_update.extend(parsed_batch_from_ai)

                self.progress_signal.emit(int((min(i + BATCH_SIZE_AI, total_to_process) / total_to_process) * 100))

            self.complete_signal.emit(all_parsed_results_for_db_update)
        except Exception as e:
            self.error_signal.emit(f"Critical error in BatchResumeAIUpdater thread: {str(e)}")
            self.complete_signal.emit([]) # Emit empty list on critical error

    def _create_ai_batch_prompt(self, resume_details_list_for_ai):
        # resume_details_list_for_ai: list of {'resume_id': id, 'filename': fn, 'text': rt}
        base_instructions = self.util_parser.create_batch_prompt([]).split("Here are the resumes to process")[0]
        instructions = base_instructions.replace(
            "0. Meta Information (include this in your JSON output for each resume):\n           - original_filename: The filename provided as 'Filename: ...' before the resume text. This is crucial for mapping.",
            "0. Meta Information (include this in your JSON output for each resume):\n           - resume_id: The ID provided as 'ID: ...' before the resume text. This is crucial for mapping."
        ) # Ensure this replacement is what's intended for the prompt.
        instructions = instructions.replace("original_filename (MUST be the filename provided", "resume_id (MUST be the ID provided")
        instructions += (
            "\nIMPORTANT: Ensure that each JSON object you generate is syntactically correct and strictly follows JSON formatting rules. "
            "Pay very close attention to: " # Corrected comma rule for last item.
            "1. Commas: Ensure all items in a list are separated by commas, and that there is NO comma after the last item if it's the final element of an enclosing object or array. Also, ensure all key-value pairs in an object are separated by commas (except the last pair). "
            "2. Quotes: All keys and string values must be enclosed in double quotes. "
            "3. Brackets and Braces: Ensure proper matching and nesting of square brackets [] for arrays/lists and curly braces {} for objects."
        )
        prompt = instructions + "\n\nHere are the resumes to process (ensure each output JSON includes 'resume_id' and is followed by '/resumesave'):\n\n"
        for resume_detail in resume_details_list_for_ai:
            prompt += f"RESUME (ID: {resume_detail['resume_id']}, Filename: {resume_detail['filename']}):\n{resume_detail['text']}\n\n"
        prompt += "\nRemember to include the '/resumesave' marker after each resume's JSON data."
        return prompt

class KeywordStatusWidget(QWidget):
    def __init__(self, term_text, parent=None):
        super().__init__(parent)
        self.term_text = term_text
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 2, 4, 2)
        self.term_label = QLabel(self.term_text)
        self.status_label = QLabel("[?]")
        self.status_label.setFixedWidth(25)
        self.status_label.setAlignment(Qt.AlignCenter)

        font = self.status_label.font()
        font.setPointSize(font.pointSize() + 1)
        self.status_label.setFont(font)

        layout.addWidget(self.term_label)
        layout.addWidget(self.status_label)
        layout.addStretch(1)

    def setStatus(self, found: bool):
        self.status_label.setText("✔️" if found else "✖️")
        self.status_label.setStyleSheet(f"color: {'green' if found else 'red'};")

    def resetStatus(self):
        self.status_label.setText("[?]")
        self.status_label.setStyleSheet("")
    def _parse_ai_batch_response(self, response_text, id_to_original_details_map_for_batch):
        parsed_results = []
        parts = response_text.split("/resumesave")
        for i, part_str in enumerate(parts): # Added enumerate for part index
            part_trimmed = part_str.strip()
            if not part_trimmed:
                continue
            try:
                ai_data, json_error_msg = _try_robust_json_loads(part_trimmed)
                if ai_data is None:
                    self.error_signal.emit(f"BatchResumeAIUpdater: Part {i+1} - Failed to parse JSON: {json_error_msg}. Skipping this AI part.")
                    continue
                ai_resume_id_raw = ai_data.get('resume_id')
                if ai_resume_id_raw is None:
                    self.error_signal.emit(f"BatchResumeAIUpdater: Part {i+1} - Parsed JSON is missing 'resume_id'. Skipping this AI part. JSON: {str(ai_data)[:200]}...")
                    continue

                try:
                    ai_resume_id = int(ai_resume_id_raw)
                except ValueError:
                    self.error_signal.emit(f"BatchResumeAIUpdater: Part {i+1} - 'resume_id' ('{ai_resume_id_raw}') is not a valid integer. Skipping this AI part. JSON: {str(ai_data)[:200]}...")
                    continue

                if ai_resume_id in id_to_original_details_map_for_batch:
                    processed_data = self.util_parser.process_resume_data(ai_data)
                    parsed_results.append((ai_resume_id, processed_data))
                else:
                    self.error_signal.emit(f"BatchResumeAIUpdater: Part {i+1} - Parsed 'resume_id' ({ai_resume_id}) not found in current batch's input IDs. Skipping this AI part. JSON: {str(ai_data)[:200]}...")

            except Exception as e: # Catch other potential errors
                self.error_signal.emit(f"Bulk AI Update: Part {i+1} - Unexpected error processing AI part: {str(e)}. Data: {part_trimmed[:200]}...")
        return parsed_results

def main():
    """Main application entry point"""
    os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--disable-extensions"

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
#